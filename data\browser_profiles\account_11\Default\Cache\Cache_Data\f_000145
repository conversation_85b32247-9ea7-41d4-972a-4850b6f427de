!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o();else if("function"==typeof define&&define.amd)define([],o);else{var t=o();for(var n in t)("object"==typeof exports?exports:e)[n]=t[n]}}(window,(function(){return h=[function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.isValidFunction=o.getPropertyValue=o.isPropertyOfType=o.detectStringOrNumberValueMismatch=o.detectArrayValuesMismatch=o.isDesktop=o.isNullOrUndefined=o.isChromeFamily=o.sample=o.detachAllHandlers=o.setInterval=o.setTimeout=o.getFnName=o.isImplNative=o.getHighestAccessibleWindow=o.getErrorReporter=o.setErrorReporter=o.getEventUtils=o.setEventUtils=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=t(3),r=null,s={},m={},c=-1!==window.location.href.indexOf("csm_debug_mode"),a=[9,16,21,30];o.setEventUtils=function(e){s=e},o.getEventUtils=function(){return s},o.setErrorReporter=function(e){m=e},o.getErrorReporter=function(){return m},o.getHighestAccessibleWindow=function(){return r=r||function(){for(var e=window,o=!1,t=0;e!==top&&e.parent&&e!=e.parent&&!1===o&&t<=10;)try{t++;var n=e.parent;n.document&&n.document.body&&n.document.body.innerHTML&&(e=e.parent)}catch(e){o=!0;break}return e}()},o.isImplNative=function(e){try{var o=new RegExp("\\{\\s*\\[native code\\]\\s*\\}","m");return o.test(e.toString())&&o.test(Function.prototype.toString.call(e))&&o.test(Function.prototype.toString.toString.call(e))&&o.test(Function.prototype.toString.toString.toString.call(e))}catch(e){return!1}},o.getFnName=function(e){for(var o="",t=0;t<a.length;t++)o+="TmIENvZGUaF0aXZlt98s7oshd4ERdsbaytYuY/".charAt(a[t]);return window[o](e)},o.setTimeout=function(e,o){return window.setTimeout((function(){try{e.call()}catch(e){e.message&&i.amzncsm.log("Error: "+e.message),m.reportError(e)}}),o)},o.setInterval=function(e,o){return window.setInterval((function(){try{e.call()}catch(e){e.message&&i.amzncsm.log("Error: "+e.message),m.reportError(e)}}),o)},o.detachAllHandlers=function(){for(var e,o=0;o<s.eventHandlers.length;o++)e=s.eventHandlers[o],s.removeEvent(e.elem,e.eventName,e.cb)},o.sample=function(e,o){if("function"==typeof e){var t=Math.random();(c||t<o/100)&&e.call(self)}},o.isChromeFamily=function(){var e=window.navigator,o=e.vendor,t=void 0!==window.opr,n=-1<e.userAgent.indexOf("Edge"),i=/Chrome/.test(e.userAgent);return/Google Inc\./.test(o)&&i&&!t&&!n},o.isNullOrUndefined=function(e){try{for(var o=Array.prototype.slice.call(arguments,1),t=0;t<o.length;t++){if(!e)return!0;var n=e[o[t]];if(null==n)return!0;e=n}return!1}catch(e){return!0}},o.isDesktop=function(){return null===navigator.userAgent.match(/(iPad|iPhone|iPod|android|webOS)/i)},o.detectArrayValuesMismatch=function(e,o){var t=!1;return!(!e&&!o)&&(e&&o?e.length!==o.length?t=!0:0<e.length&&0<o.length&&"string"==typeof e[0]&&e[0]!==o[0]&&(t=!0):t=!0,t)},o.detectStringOrNumberValueMismatch=function(e,o){return!(!e&&!o)&&e!==o},o.isPropertyOfType=function(e,o){try{if(!e)return!1;for(var t=Array.prototype.slice.call(arguments,2),i=0;i<t.length;i++){var r=e[t[i]];if(null==r)return i===t.length-1&&(void 0===r?"undefined":n(r))===o;e=r}return("undefined"==typeof propValue?"undefined":n(propValue))===o}catch(e){return!1}},o.getPropertyValue=function(e){try{if(!e)return e;for(var o,t=Array.prototype.slice.call(arguments,1),n=0;n<t.length;n++){if(!(o=e[t[n]]))return o;e=o}return o}catch(e){return null}},o.isValidFunction=function(e){return null!=e&&"function"==typeof e}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.getErrorHandler=d,o.reportError=function(e){d().reportError(e)};var n=t(0),i=t(3),r=t(6),s=(0,n.getHighestAccessibleWindow)(),m=null;function c(e){i.amzncsm.log(e)}var a=function(e){m=new r.HandleExceptions(e),s[m]=m};function d(){return m||(a(c),m)}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.LIBRARY_VERSION="1.0.0",o.VFRD_CODE_HEADLESS_BROWSER=4,o.VFRD_CODE_HEADLESS_BROWSER_TEST=8,o.HEADLESS_CODE_CHROME="chrm",o.HEADLESS_CODE_CHROME_OBJECT_INTERNALS_AVAIL="chrm1",o.HEADLESS_CODE_CHROME_OBJECT_MISSING="chrm2",o.HEADLESS_CODE_CHROME_INSTALL="chrm3",o.HEADLESS_CODE_FF="ffox",o.HEADLESS_CODE_PHANTOMJS="phnt",o.HEADLESS_CODE_MOUSE_MOVEMENT_IN_HIDDEN_TAB="mmh",o.HEADLESS_CODE_ZERO_WINDOW_DIMENSION="zdim",o.HEADLESS_CODE_SINGLE_TOUCH_POINTS="otch",o.ERROR_BASED_TOOLKIT_DETECTION="estr",o.HARDWARE_CONCURRENCY_MISMATCH="spfp2",o.FORENSICS_OBJECT="forensics",o.ZONE_REGION_MAP={USEast:"na","US-PDX":"na","US-IAD":"na",EU:"eu","FE-SIN":"fe","FE-PDX":"fe"},o.TUNGSTEN_TENANTS={forensics3PCsmEvent:"3pCsmEvent",forensicsTwitchCsmEvent:"twitchCsmEvent",forensicsImdbCsmEvent:"imdbCsmEvent",forensicsDefaultCsmEvent:"defaultCsmEvent",shadowCsmEvent:"shadowEvent"},o.VFRD_CODES={APS_VFRD_CODE:21,_3PX_VFRD_CODE:22,TWITCH_VFRD_CODE:23,IMDB_VFRD_CODE:24,DEFAULT_VFRD_CODE:25},o.SOURCE_IDS={TWITCH_SOURCE_IDS:["650","651","652","654"],IMDB_SOURCE_IDS:["204"]},o.SOURCE_TYPE={APS_SOURCE_TYPE:"dtb",_3PX_SOURCE_TYPE:"rtb"},o.MAX_DEPTH=3,o.VFRD_CODE=21,o.CSM_VERSION="r-1.31",o.SSI_CSM_VERSION="ccf-1.2",o.SESSION_START_EVENT="sessionStart"},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0});var n=o.amzncsm=n||{};n.log=function(e){try{-1!==window.location.href.indexOf("csm_debug_mode")&&window.console&&window.console.log(e)}catch(e){window.console.log(e.message)}}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.addSSITungstenPixel=o.addNexusPixel=o.addErrorPixel=o.init=o.addPixel=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=t(2),r=t(3),s=!1,m={},c={};o.addPixel=function(e,o,t){var a,d,l,u=(a=e,d=t,l={vfrd:1===o?i.VFRD_CODE_HEADLESS_BROWSER_TEST:i.VFRD_CODE_HEADLESS_BROWSER,dbg:a},"object"===(void 0===d?"undefined":n(d))?l.info=JSON.stringify(d):"string"==typeof d?l.info=d:"number"==typeof d&&(l.info=d.toString()),l);if(window.RUNNING_TESTS&&TEST_IDS.push(u),!m.allowOnlyOneVfrdPixel||!s){r.amzncsm.log("Pixel = "+JSON.stringify(u));var $=c.addPixel,p=void 0===$?function(){}:$,f=c.logForensicsCsmEventToTungsten,g=void 0===f?function(){}:f;p.call(c,u),g.call(c,u),s=!0}},o.init=function(e,o){c=e,m=o},o.addErrorPixel=function(e){window.RUNNING_TESTS&&TEST_IDS.push(e),r.amzncsm.log("Pixel = "+JSON.stringify(e));var o=c.firePixel,t=void 0===o?function(){}:o,n=c.logForensicsCsmEventToTungsten,i=void 0===n?function(){}:n;t.call(c,JSON.stringify(e),"");var s=e.adViewability[0].error;return i.call(c,s,!0),!0},o.addNexusPixel=function(e,o,t){var n=window.ue_csm,i={server:window.location.hostname,fmp:e};n&&n.ue&&n.ue.event&&n.ue.event(i,o,t)},o.addSSITungstenPixel=function(e,o){try{var t={vfrd:e,dbg:JSON.stringify(o)};window.RUNNING_TESTS&&TEST_IDS.push(t),r.amzncsm.log("Pixel = "+JSON.stringify(t)),c.addPixel;var n=c.logForensicsCsmEventToTungsten;(void 0===n?function(){}:n).call(c,t)}catch(e){r.amzncsm.log("error logging pixel "+JSON.stringify(o))}}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.deleteForensicsObject=o.addObjToForensicsObj=void 0;var n=t(0),i=t(1),r=t(2),s=(0,n.getHighestAccessibleWindow)();o.addObjToForensicsObj=function(e,o){try{var t=s[r.FORENSICS_OBJECT]||{};t[e]=o,s[r.FORENSICS_OBJECT]=t}catch(e){(0,i.reportError)(e)}},o.deleteForensicsObject=function(){s[r.FORENSICS_OBJECT]=null,delete s[r.FORENSICS_OBJECT]}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.HandleExceptions=void 0;function n(e,o){for(var t=0;t<o.length;t++){var n=o[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var i=t(0),r=t(3);function s(e){return e}function m(e,o){var t=1<arguments.length&&void 0!==o?o:"";return e.m&&e.m.message?t+=e.m.message:e.m&&e.m.target&&e.m.target.tagName?t+="Error handler invoked by "+e.m.target.tagName+" tag":e.m?t+=e.m:e.message?t+=e.message:t+="Unknown error",t}var c=(0,i.getHighestAccessibleWindow)();function a(e){r.amzncsm.log(e)}function d(e){!function(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}(this,d),this._shouldLogToConsole=!1,this._consoleString="console",this._errorString="error",this._extractErrorMessage=m,this._buildErrorPixel=s,(0,i.isValidFunction)(e)?this._logErrorPixel=e:this._logErrorPixel=a}o.HandleExceptions=(function(e,o,t){o&&n(e.prototype,o),t&&n(e,t)}(d,[{key:"setShouldLogToConsole",value:function(e){this._shouldLogToConsole=!0===e}},{key:"setConsoleString",value:function(e){this._consoleString=e}},{key:"setErrorString",value:function(e){this._errorString=e}},{key:"setExtractErrorMessage",value:function(e){(0,i.isValidFunction)(e)&&(this._extractErrorMessage=e)}},{key:"setBuildErrorPixel",value:function(e){(0,i.isValidFunction)(e)&&(this._buildErrorPixel=e)}},{key:"setLogErrorPixel",value:function(e){(0,i.isValidFunction)(e)&&(this._logErrorPixel=e)}},{key:"consoleLog",value:function(e){c[this.console_string][this.error_string](e)}},{key:"reportError",value:function(e,o){var t=1<arguments.length&&void 0!==o?o:"";try{var n=this._extractErrorMessage(e,t),i=this._buildErrorPixel(n);this.shouldLogToConsole&&this.consoleLog(e),this._logErrorPixel(i)}catch(e){r.amzncsm.log(e.message)}}}]),d)},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.ecclCSMAttributes=void 0;var n=t(8),i=t(9),r=t(0),s=t(5),m=t(1);o.ecclCSMAttributes=function(e,o,t,c,a,d,l){try{if(!(0,r.isValidFunction)(l))throw new Error("logger is null or not a function");var u={};(0,r.isValidFunction)(o)&&o(),u=(0,r.isValidFunction)(t)?t(e):(0,n.collectWithSchemaHavingNestedObjects)(e,null,null,0,d),(0,s.deleteForensicsObject)(),(0,r.isValidFunction)(c)?u=c(u,e):a&&(u=(0,i.compressWithSchemaNestedObjects)(u,e,null,0,d)),l(u)}catch(e){(0,m.reportError)(e)}}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.collectWithSchemaHavingNestedObjects=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=t(0),r=t(1),s=(0,i.getHighestAccessibleWindow)();o.collectWithSchemaHavingNestedObjects=function e(o,t,i,m,c){var a=t||s,d=i||o,l={};try{if(c<m)return{};var u=void 0;Object.keys(d).forEach((function(t){"object"!==(void 0===(u=d[t])?"undefined":n(u))||Array.isArray(u)?l[t]=a[t]:null==a[t]?l[t]=null:l[t]=e(o,a[t],d[t],m+1,c)}))}catch(t){(0,r.reportError)(t)}return l}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.compressWithSchemaNestedObjects=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=t(1);o.compressWithSchemaNestedObjects=function e(o,t,r,s,m){var c=[];try{if(m<=s)return[];var a=r||t,d=Object.keys(a),l=0;d.forEach((function(i){if("object"!=n(a[i])||null==a[i]||Array.isArray(a[i]))switch(!0){case!0===o[i]:c[l]=1;break;case!1===o[i]:case 0===o[i]:c[l]=0;break;case null!=o[i]:c[l]=o[i];break;default:c[l]=null}else null!=o[i]?c[l]=e(o[i],t,a[i],s+1):c[l]=e({},t,a[i],s+1);l+=1}))}catch(r){(0,i.reportError)(r)}return c}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.csmAttribute={screen:{availHeight:0,availWidth:0,width:0,height:0,colorDepth:0,pixelDepth:0,orientation:{angle:0,onchange:0},isExtended:0},navigator:{appCodeName:"",appName:"",appVersion:"",deviceMemory:0,doNotTrack:0,hardwareConcurrency:0,language:"",maxTouchPoints:0,product:"",productSub:0,vendor:"",vendorSub:"",userAgent:"",languages:[],platform:"",webdriver:0,bluetooth:"",pdfViewerEnabled:0,permissions:"",oscpu:"",userAgentData:"",msMaxTouchPoints:"",msPointerEnabled:0,connection:{rtt:0,downlink:0,effectiveType:"",saveData:0}},window:{innerHeight:0,innerWidth:0,outerWidth:0,outerHeight:0,ondevicemotion:"",chrome:"",safari:"",InstallTrigger:0,__crWeb:"",__gCrWeb:"",yandex:"",__yb:"",__ybro:"",__firefox__:"",__edgeTrackingPreventionStatistics:"",webkit:"",opera:"",opr:"",oprt:"",samsungAr:"",ucweb:"",UCShellJava:"",puffinDevice:"",ontouchstart:"",devicePixelRatio:0,_selenium:"",callSelenium:"",_phantom:"",callPhantom:"",__phantomas:"",Buffer:"",spawn:"",domAutomationController:"",__nightmare:"",$chrome:"",$cdc_asdjflasutopfhvcZLmcfl_:"",__webdriver_evaluate:"",__selenium_evaluate:"",__webdriver_script_function:"",__webdriver_script_func:"",__webdriver_script_fn:"",__fxdriver_evaluate:"",__driver_unwrapped:"",__webdriver_unwrapped:"",__driver_evaluate:"",__selenium_unwrapped:"",__fxdriver_unwrapped:"",cdc_adoQpoasnfa76pfcZLmcfl_Array:"",cdc_adoQpoasnfa76pfcZLmcfl_Object:"",cdc_adoQpoasnfa76pfcZLmcfl_Promise:"",cdc_adoQpoasnfa76pfcZLmcfl_Proxy:"",cdc_adoQpoasnfa76pfcZLmcfl_Symbol:"",cdc_adoQpoasnfa76pfcZLmcfl_JSON:"",length:""},document:{referer:"",height:"",width:"",visibilityState:"",documentElement:{clientHeight:0,clientWidth:0},body:{clientHeight:0,clientWidth:0,scrollHeight:0,scrollWidth:0,offsetHeight:0,offsetWidth:0},activeElement:{tagName:""}},history:{length:""},location:{href:""},forensics:{pluginsLength:"",mimeTypesLength:"",timeZone:"",regExpLength:"",documentHasFocus:0,arch:"",navigatorUserAgentByDescriptor:"",navigatorUserAgentGetter:"",isGlobalThisSupported:"",isWebKitPointSupported:"",isApplePaySessionSupported:"",windowPropertiesLength:"",navigatorPropertiesLength:"",documentPropertiesLength:"",errorPropertiesLength:"",setPropertiesLength:"",workerPropertiesLength:"",cssStyleDeclarationPropertiesLength:"",navigatorWebDriverByDescriptor:""}}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.log=o.postRequest=o.logToTungsten=o.logWithNexus=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=t(4),r=t(1),s=function(e){if(e&&e.__esModule)return e;var o={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(o[t]=e[t]);return o.default=e,o}(t(2));o.logWithNexus=function(e,o,t,s,m,c){try{var a={vfrd:1===o?s:t};"object"===(void 0===e?"undefined":n(e))?a.info=JSON.stringify(e):"string"==typeof e?a.info=e:"number"==typeof e&&(a.info=e.toString()),(0,i.addNexusPixel)(a,m,c)}catch(e){return(0,r.reportError)(e),!1}return!0},o.logToTungsten=function(e,o,t,n,i,r,s){var l=6<arguments.length&&void 0!==s&&s;try{var u=m(o,t,i,r,l),$=c(e,n);a($,u,(function(e){d(e.message)}))}catch(e){d(e.message)}};var m=function(e,o,t,n,i){var r=4<arguments.length&&void 0!==i&&i;if(!o)throw new Error("bidId is not defined");return[{ver:n,ts:(new Date).getTime(),error:r?1:0,bidId:o,fmp:r?e:{vfrd:t,dbg:e.dbg}}]},c=function(e,o){if(!o)throw new Error("Zone is not defined");var t=s.ZONE_REGION_MAP[o];if(!t)throw new Error("Region is not defined");return"https://tungsten-service.prod."+t+".adsqtungsten.a9.amazon.dev/csm/"+e},a=o.postRequest=function(e,o,t){fetch(e,{cache:"no-cache",method:"POST",body:JSON.stringify(o),headers:new Headers({"Content-Type":"application/json"})}).then((function(e){if(200!==e.status)throw new Error("Tungsten returned response status: "+e.status)})).catch((function(e){t&&t(e)}))},d=o.log=function(e){try{-1!==window.location.href.indexOf("csm_debug_mode")&&window.console&&window.console.log(e)}catch(e){window.console.log(e.message)}}},,,,,,,,,,,,,function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.init=void 0;var n=t(7),i=t(10),r=t(2),s=function(e){if(e&&e.__esModule)return e;var o={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(o[t]=e[t]);return o.default=e,o}(r),m=t(1),c=t(25),a=t(26),d=t(28);function l(e,o){var t=1<arguments.length&&void 0!==o&&o;if(f)(0,d.extractRequiredParamsAndLog)(f,e,t);else{var n=g();if(n)return f=n,void(0,d.extractRequiredParamsAndLog)(n,e,t);!function(e,o){var t=1<arguments.length&&void 0!==o&&o;(new a.OmidVerificationClient).registerSessionObserver((function(o){if(o.type===r.SESSION_START_EVENT){var n=(0,d.getVerificationParamsFromSessionStart)(o);if((0,d.isLoggingParamsValid)(n))return f=n,void(0,d.extractRequiredParamsAndLog)(n,e,t);throw new Error("Unable to fetch bidId or zone or tenantId from logging parameters")}}))}(e,t)}}function u(e){l({dbg:e},!1)}function $(e){l({m:e},!0)}function p(){try{(0,m.getErrorHandler)().setLogErrorPixel($),(0,n.ecclCSMAttributes)(i.csmAttribute,null,null,null,!0,s.MAX_DEPTH,u)}catch(e){(0,m.reportError)(e)}}var f=void 0,g=function(){if("undefined"!=typeof window?window.document:document)try{var e=(0,c.getParametersFromTagstone)();if((0,d.isLoggingParamsValid)(e))return e}catch(e){return}};p(),o.init=p},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.getParametersFromTagstone=function(){var e="undefined"!=typeof window?window.document:document;if(!e||!e.currentScript||!e.currentScript.src)throw new Error("Error accessing current script source");var o=new URL(e.currentScript.src);return Object.fromEntries(new URLSearchParams(o.search))}},function(module,exports,__webpack_require__){"use strict";(function(global){var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ei,fi,gi,hi,ji;ei=void 0===global?void 0:global,fi=function(omidGlobal,omidExports){var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.arrayIteratorImpl=function(e){var o=0;return function(){return o<e.length?{done:!1,value:e[o++]}:{done:!0}}},$jscomp.arrayIterator=function(e){return{next:$jscomp.arrayIteratorImpl(e)}},$jscomp.makeIterator=function(e){var o="undefined"!=typeof Symbol&&Symbol.iterator&&e[Symbol.iterator];return o?o.call(e):$jscomp.arrayIterator(e)},$jscomp.arrayFromIterator=function(e){for(var o,t=[];!(o=e.next()).done;)t.push(o.value);return t},$jscomp.arrayFromIterable=function(e){return e instanceof Array?e:$jscomp.arrayFromIterator($jscomp.makeIterator(e))},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.objectCreate=$jscomp.ASSUME_ES5||"function"==typeof Object.create?Object.create:function(e){function o(){}return o.prototype=e,new o},$jscomp.underscoreProtoCanBeSet=function(){var e={};try{return e.__proto__={a:!0},e.a}catch(e){}return!1},$jscomp.setPrototypeOf="function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(e,o){if(e.__proto__=o,e.__proto__!==o)throw new TypeError(e+" is not extensible");return e}:null,$jscomp.inherits=function(e,o){if(e.prototype=$jscomp.objectCreate(o.prototype),e.prototype.constructor=e,$jscomp.setPrototypeOf){var t=$jscomp.setPrototypeOf;t(e,o)}else for(t in o)if("prototype"!=t)if(Object.defineProperties){var n=Object.getOwnPropertyDescriptor(o,t);n&&Object.defineProperty(e,t,n)}else e[t]=o[t];e.superClass_=o.prototype};var module$exports$omid$common$argsChecker={assertTruthyString:function(e,o){if(!o)throw Error("Value for "+e+" is undefined, null or blank.");if("string"!=typeof o&&!(o instanceof String))throw Error("Value for "+e+" is not a string.");if(""===o.trim())throw Error("Value for "+e+" is empty string.")},assertNotNullObject:function(e,o){if(null==o)throw Error("Value for "+e+" is undefined or null")},assertNumber:function(e,o){if(null==o)throw Error(e+" must not be null or undefined.");if("number"!=typeof o||isNaN(o))throw Error("Value for "+e+" is not a number")},assertNumberBetween:function(e,o,t,n){if((0,module$exports$omid$common$argsChecker.assertNumber)(e,o),o<t||n<o)throw Error("Value for "+e+" is outside the range ["+t+","+n+"]")},assertFunction:function(e,o){if(!o)throw Error(e+" must not be truthy.")},assertPositiveNumber:function(e,o){if((0,module$exports$omid$common$argsChecker.assertNumber)(e,o),o<0)throw Error(e+" must be a positive number.")}},module$exports$omid$common$VersionUtils={},module$contents$omid$common$VersionUtils_SEMVER_DIGITS_NUMBER=3;module$exports$omid$common$VersionUtils.isValidVersion=function(e){return/\d+\.\d+\.\d+(-.*)?/.test(e)},module$exports$omid$common$VersionUtils.versionGreaterOrEqual=function(e,o){e=e.split("-")[0].split("."),o=o.split("-")[0].split(".");for(var t=0;t<module$contents$omid$common$VersionUtils_SEMVER_DIGITS_NUMBER;t++){var n=parseInt(e[t],10),i=parseInt(o[t],10);if(i<n)break;if(n<i)return!1}return!0};var module$exports$omid$common$ArgsSerDe={},module$contents$omid$common$ArgsSerDe_ARGS_NOT_SERIALIZED_VERSION="1.0.3";module$exports$omid$common$ArgsSerDe.serializeMessageArgs=function(e,o){return(0,module$exports$omid$common$VersionUtils.isValidVersion)(e)&&(0,module$exports$omid$common$VersionUtils.versionGreaterOrEqual)(e,module$contents$omid$common$ArgsSerDe_ARGS_NOT_SERIALIZED_VERSION)?o:JSON.stringify(o)},module$exports$omid$common$ArgsSerDe.deserializeMessageArgs=function(e,o){return(0,module$exports$omid$common$VersionUtils.isValidVersion)(e)&&(0,module$exports$omid$common$VersionUtils.versionGreaterOrEqual)(e,module$contents$omid$common$ArgsSerDe_ARGS_NOT_SERIALIZED_VERSION)?o||[]:o&&"string"==typeof o?JSON.parse(o):[]};var module$exports$omid$common$constants={AdEventType:{IMPRESSION:"impression",STATE_CHANGE:"stateChange",GEOMETRY_CHANGE:"geometryChange",SESSION_START:"sessionStart",SESSION_ERROR:"sessionError",SESSION_FINISH:"sessionFinish",MEDIA:"media",VIDEO:"video",LOADED:"loaded",START:"start",FIRST_QUARTILE:"firstQuartile",MIDPOINT:"midpoint",THIRD_QUARTILE:"thirdQuartile",COMPLETE:"complete",PAUSE:"pause",RESUME:"resume",BUFFER_START:"bufferStart",BUFFER_FINISH:"bufferFinish",SKIPPED:"skipped",VOLUME_CHANGE:"volumeChange",PLAYER_STATE_CHANGE:"playerStateChange",AD_USER_INTERACTION:"adUserInteraction"},MediaEventType:{LOADED:"loaded",START:"start",FIRST_QUARTILE:"firstQuartile",MIDPOINT:"midpoint",THIRD_QUARTILE:"thirdQuartile",COMPLETE:"complete",PAUSE:"pause",RESUME:"resume",BUFFER_START:"bufferStart",BUFFER_FINISH:"bufferFinish",SKIPPED:"skipped",VOLUME_CHANGE:"volumeChange",PLAYER_STATE_CHANGE:"playerStateChange",AD_USER_INTERACTION:"adUserInteraction"}};module$exports$omid$common$constants.VideoEventType=module$exports$omid$common$constants.MediaEventType,module$exports$omid$common$constants.ImpressionType={DEFINED_BY_JAVASCRIPT:"definedByJavaScript",UNSPECIFIED:"unspecified",LOADED:"loaded",BEGIN_TO_RENDER:"beginToRender",ONE_PIXEL:"onePixel",VIEWABLE:"viewable",AUDIBLE:"audible",OTHER:"other"},module$exports$omid$common$constants.ErrorType={GENERIC:"generic",VIDEO:"video",MEDIA:"media"},module$exports$omid$common$constants.AdSessionType={NATIVE:"native",HTML:"html",JAVASCRIPT:"javascript"},module$exports$omid$common$constants.EventOwner={NATIVE:"native",JAVASCRIPT:"javascript",NONE:"none"},module$exports$omid$common$constants.AccessMode={FULL:"full",LIMITED:"limited"},module$exports$omid$common$constants.AppState={BACKGROUNDED:"backgrounded",FOREGROUNDED:"foregrounded"},module$exports$omid$common$constants.Environment={APP:"app",WEB:"web"},module$exports$omid$common$constants.InteractionType={CLICK:"click",INVITATION_ACCEPT:"invitationAccept"},module$exports$omid$common$constants.CreativeType={DEFINED_BY_JAVASCRIPT:"definedByJavaScript",HTML_DISPLAY:"htmlDisplay",NATIVE_DISPLAY:"nativeDisplay",VIDEO:"video",AUDIO:"audio"},module$exports$omid$common$constants.MediaType={DISPLAY:"display",VIDEO:"video"},module$exports$omid$common$constants.Reason={NOT_FOUND:"notFound",HIDDEN:"hidden",BACKGROUNDED:"backgrounded",VIEWPORT:"viewport",OBSTRUCTED:"obstructed",CLIPPED:"clipped"},module$exports$omid$common$constants.SupportedFeatures={CONTAINER:"clid",VIDEO:"vlid"},module$exports$omid$common$constants.VideoPosition={PREROLL:"preroll",MIDROLL:"midroll",POSTROLL:"postroll",STANDALONE:"standalone"},module$exports$omid$common$constants.VideoPlayerState={MINIMIZED:"minimized",COLLAPSED:"collapsed",NORMAL:"normal",EXPANDED:"expanded",FULLSCREEN:"fullscreen"},module$exports$omid$common$constants.NativeViewKeys={X:"x",LEFT:"left",Y:"y",TOP:"top",WIDTH:"width",HEIGHT:"height",AD_SESSION_ID:"adSessionId",IS_FRIENDLY_OBSTRUCTION_FOR:"isFriendlyObstructionFor",CLIPS_TO_BOUNDS:"clipsToBounds",CHILD_VIEWS:"childViews",END_X:"endX",END_Y:"endY",OBSTRUCTIONS:"obstructions",OBSTRUCTION_CLASS:"obstructionClass",OBSTRUCTION_PURPOSE:"obstructionPurpose",OBSTRUCTION_REASON:"obstructionReason",PIXELS:"pixels"},module$exports$omid$common$constants.MeasurementStateChangeSource={CONTAINER:"container",CREATIVE:"creative"},module$exports$omid$common$constants.ElementMarkup={OMID_ELEMENT_CLASS_NAME:"omid-element"},module$exports$omid$common$constants.CommunicationType={NONE:"NONE",DIRECT:"DIRECT",POST_MESSAGE:"POST_MESSAGE"},module$exports$omid$common$constants.OmidImplementer={OMSDK:"omsdk"};var module$contents$omid$common$InternalMessage_GUID_KEY="omid_message_guid",module$contents$omid$common$InternalMessage_METHOD_KEY="omid_message_method",module$contents$omid$common$InternalMessage_VERSION_KEY="omid_message_version",module$contents$omid$common$InternalMessage_ARGS_KEY="omid_message_args",module$exports$omid$common$InternalMessage=function(e,o,t,n){this.guid=e,this.method=o,this.version=t,this.args=n};module$exports$omid$common$InternalMessage.isValidSerializedMessage=function(e){return!!e&&void 0!==e[module$contents$omid$common$InternalMessage_GUID_KEY]&&void 0!==e[module$contents$omid$common$InternalMessage_METHOD_KEY]&&void 0!==e[module$contents$omid$common$InternalMessage_VERSION_KEY]&&"string"==typeof e[module$contents$omid$common$InternalMessage_GUID_KEY]&&"string"==typeof e[module$contents$omid$common$InternalMessage_METHOD_KEY]&&"string"==typeof e[module$contents$omid$common$InternalMessage_VERSION_KEY]&&(void 0===e[module$contents$omid$common$InternalMessage_ARGS_KEY]||void 0!==e[module$contents$omid$common$InternalMessage_ARGS_KEY])},module$exports$omid$common$InternalMessage.deserialize=function(e){return new module$exports$omid$common$InternalMessage(e[module$contents$omid$common$InternalMessage_GUID_KEY],e[module$contents$omid$common$InternalMessage_METHOD_KEY],e[module$contents$omid$common$InternalMessage_VERSION_KEY],e[module$contents$omid$common$InternalMessage_ARGS_KEY])},module$exports$omid$common$InternalMessage.prototype.serialize=function(){var e={};return e[module$contents$omid$common$InternalMessage_GUID_KEY]=this.guid,e[module$contents$omid$common$InternalMessage_METHOD_KEY]=this.method,e[module$contents$omid$common$InternalMessage_VERSION_KEY]=this.version,e=e,void 0!==this.args&&(e[module$contents$omid$common$InternalMessage_ARGS_KEY]=this.args),e};var module$exports$omid$common$Communication=function(e){this.to=e,this.communicationType_=module$exports$omid$common$constants.CommunicationType.NONE};module$exports$omid$common$Communication.prototype.sendMessage=function(e,o){},module$exports$omid$common$Communication.prototype.handleMessage=function(e,o){this.onMessage&&this.onMessage(e,o)},module$exports$omid$common$Communication.prototype.serialize=function(e){return JSON.stringify(e)},module$exports$omid$common$Communication.prototype.deserialize=function(e){return JSON.parse(e)},module$exports$omid$common$Communication.prototype.isDirectCommunication=function(){return this.communicationType_===module$exports$omid$common$constants.CommunicationType.DIRECT},module$exports$omid$common$Communication.prototype.isCrossOrigin=function(){};var module$exports$omid$common$DetectOmid={OMID_PRESENT_FRAME_NAME:"omid_v1_present",isOmidPresent:function(e){try{return!!e.frames&&!!e.frames[module$exports$omid$common$DetectOmid.OMID_PRESENT_FRAME_NAME]}catch(e){return!1}},declareOmidPresence:function(e){e.frames&&e.document&&(module$exports$omid$common$DetectOmid.OMID_PRESENT_FRAME_NAME in e.frames||(null==e.document.body&&module$exports$omid$common$DetectOmid.isMutationObserverAvailable_(e)?module$exports$omid$common$DetectOmid.registerMutationObserver_(e):e.document.body?module$exports$omid$common$DetectOmid.appendPresenceIframe_(e):e.document.write('<iframe style="display:none" id="'+module$exports$omid$common$DetectOmid.OMID_PRESENT_FRAME_NAME+'" name="'+module$exports$omid$common$DetectOmid.OMID_PRESENT_FRAME_NAME+'"></iframe>')))},appendPresenceIframe_:function(e){var o=e.document.createElement("iframe");o.id=module$exports$omid$common$DetectOmid.OMID_PRESENT_FRAME_NAME,o.name=module$exports$omid$common$DetectOmid.OMID_PRESENT_FRAME_NAME,o.style.display="none",e.document.body.appendChild(o)},isMutationObserverAvailable_:function(e){return"MutationObserver"in e},registerMutationObserver_:function(e){var o=new MutationObserver((function(t){t.forEach((function(t){"BODY"===t.addedNodes[0].nodeName&&(module$exports$omid$common$DetectOmid.appendPresenceIframe_(e),o.disconnect())}))}));o.observe(e.document.documentElement,{childList:!0})}},module$exports$omid$common$DirectCommunication=function e(o){module$exports$omid$common$Communication.call(this,o),this.communicationType_=module$exports$omid$common$constants.CommunicationType.DIRECT,this.handleExportedMessage=e.prototype.handleExportedMessage.bind(this)};$jscomp.inherits(module$exports$omid$common$DirectCommunication,module$exports$omid$common$Communication),module$exports$omid$common$DirectCommunication.prototype.sendMessage=function(e,o){if(!(o=void 0===o?this.to:o))throw Error("Message destination must be defined at construction time or when sending the message.");o.handleExportedMessage(e.serialize(),this)},module$exports$omid$common$DirectCommunication.prototype.handleExportedMessage=function(e,o){module$exports$omid$common$InternalMessage.isValidSerializedMessage(e)&&this.handleMessage(module$exports$omid$common$InternalMessage.deserialize(e),o)},module$exports$omid$common$DirectCommunication.prototype.isCrossOrigin=function(){return!1};var module$exports$omid$common$eventTypedefs={},module$exports$omid$common$exporter={};function module$contents$omid$common$exporter_getOmidExports(){return void 0===omidExports?null:omidExports}function module$contents$omid$common$exporter_getOrCreateName(e,o){return e&&(e[o]||(e[o]={}))}module$exports$omid$common$exporter.packageExport=function(e,o,t){(t=void 0===t?module$contents$omid$common$exporter_getOmidExports():t)&&((e=e.split(".")).slice(0,e.length-1).reduce(module$contents$omid$common$exporter_getOrCreateName,t)[e[e.length-1]]=o)};var module$exports$omid$common$guid={generateGuid:function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var o=16*Math.random()|0;return"y"===e?(3&o|8).toString(16):o.toString(16)}))}},module$exports$omid$common$logger={error:function(e){for(var o=[],t=0;t<arguments.length;++t)o[t-0]=arguments[t];module$contents$omid$common$logger_executeLog((function(){throw new(Function.prototype.bind.apply(Error,[null,"Could not complete the test successfully - "].concat($jscomp.arrayFromIterable(o))))}),(function(){}))},debug:function(e){for(var o=[],t=0;t<arguments.length;++t)o[t-0]=arguments[t];module$contents$omid$common$logger_executeLog((function(){}),(function(){}))}};function module$contents$omid$common$logger_executeLog(e,o){"undefined"!=typeof jasmine&&jasmine?e():"undefined"!=typeof console&&console&&console.error&&o()}var module$exports$omid$common$OmidGlobalProvider={},module$contents$omid$common$OmidGlobalProvider_globalThis=eval("this");function module$contents$omid$common$OmidGlobalProvider_getOmidGlobal(){if(void 0!==omidGlobal&&omidGlobal)return omidGlobal;if(void 0!==global&&global)return global;if("undefined"!=typeof window&&window)return window;if(void 0!==module$contents$omid$common$OmidGlobalProvider_globalThis&&module$contents$omid$common$OmidGlobalProvider_globalThis)return module$contents$omid$common$OmidGlobalProvider_globalThis;throw Error("Could not determine global object context.")}module$exports$omid$common$OmidGlobalProvider.omidGlobal=module$contents$omid$common$OmidGlobalProvider_getOmidGlobal();var module$exports$omid$common$windowUtils={};function module$contents$omid$common$windowUtils_isValidWindow(e){return null!=e&&void 0!==e.top&&null!=e.top}function module$contents$omid$common$windowUtils_isSameOriginForIE(e){return""===e.x||""!==e.x}module$exports$omid$common$windowUtils.isCrossOrigin=function(e){if(e===module$exports$omid$common$OmidGlobalProvider.omidGlobal)return!1;try{if(void 0===e.location.hostname)return!0;module$contents$omid$common$windowUtils_isSameOriginForIE(e)}catch(e){return!0}return!1},module$exports$omid$common$windowUtils.resolveGlobalContext=function(e){return void 0===e&&"undefined"!=typeof window&&window&&(e=window),module$contents$omid$common$windowUtils_isValidWindow(e)?e:module$exports$omid$common$OmidGlobalProvider.omidGlobal},module$exports$omid$common$windowUtils.resolveTopWindowContext=function(e){return module$contents$omid$common$windowUtils_isValidWindow(e)?e.top:module$exports$omid$common$OmidGlobalProvider.omidGlobal},module$exports$omid$common$windowUtils.evaluatePageUrl=function(e){if(!module$contents$omid$common$windowUtils_isValidWindow(e))return null;try{var o=e.top;return(0,module$exports$omid$common$windowUtils.isCrossOrigin)(o)?null:o.location.href}catch(e){return null}};var module$exports$omid$common$PostMessageCommunication=function(e,o){o=void 0===o?module$exports$omid$common$OmidGlobalProvider.omidGlobal:o,module$exports$omid$common$Communication.call(this,o);var t=this;this.communicationType_=module$exports$omid$common$constants.CommunicationType.POST_MESSAGE,e.addEventListener("message",(function(e){if("object"===_typeof(e.data)){var o=e.data;module$exports$omid$common$InternalMessage.isValidSerializedMessage(o)&&(o=module$exports$omid$common$InternalMessage.deserialize(o),e.source&&t.handleMessage(o,e.source))}}))};$jscomp.inherits(module$exports$omid$common$PostMessageCommunication,module$exports$omid$common$Communication),module$exports$omid$common$PostMessageCommunication.isCompatibleContext=function(e){return!!(e&&e.addEventListener&&e.postMessage)},module$exports$omid$common$PostMessageCommunication.prototype.sendMessage=function(e,o){if(!(o=void 0===o?this.to:o))throw Error("Message destination must be defined at construction time or when sending the message.");o.postMessage(e.serialize(),"*")},module$exports$omid$common$PostMessageCommunication.prototype.isCrossOrigin=function(){return!this.to||(0,module$exports$omid$common$windowUtils.isCrossOrigin)(this.to)};var module$exports$omid$common$Rectangle=function(e,o,t,n){this.x=e,this.y=o,this.width=t,this.height=n},module$exports$omid$common$serviceCommunication={},module$contents$omid$common$serviceCommunication_EXPORTED_SESSION_COMMUNICATION_NAME=["omid","v1_SessionServiceCommunication"],module$contents$omid$common$serviceCommunication_EXPORTED_VERIFICATION_COMMUNICATION_NAME=["omid","v1_VerificationServiceCommunication"],module$contents$omid$common$serviceCommunication_EXPORTED_SERVICE_WINDOW_NAME=["omidVerificationProperties","serviceWindow"];function module$contents$omid$common$serviceCommunication_getValueForKeypath(e,o){return o.reduce((function(e,o){return e&&e[o]}),e)}function module$contents$omid$common$serviceCommunication_startServiceCommunication(e,o,t,n){if(!(0,module$exports$omid$common$windowUtils.isCrossOrigin)(o))try{var i=module$contents$omid$common$serviceCommunication_getValueForKeypath(o,t);if(i)return new module$exports$omid$common$DirectCommunication(i)}catch(e){}return n(o)?new module$exports$omid$common$PostMessageCommunication(e,o):null}function module$contents$omid$common$serviceCommunication_startServiceCommunicationFromCandidates(e,o,t,n){for(var i=(o=$jscomp.makeIterator(o)).next();!i.done;i=o.next())if(i=module$contents$omid$common$serviceCommunication_startServiceCommunication(e,i.value,t,n))return i;return null}module$exports$omid$common$serviceCommunication.startSessionServiceCommunication=function(e,o,t){t=void 0===t?module$exports$omid$common$DetectOmid.isOmidPresent:t;var n=[e,(0,module$exports$omid$common$windowUtils.resolveTopWindowContext)(e)];return o&&n.unshift(o),module$contents$omid$common$serviceCommunication_startServiceCommunicationFromCandidates(e,n,module$contents$omid$common$serviceCommunication_EXPORTED_SESSION_COMMUNICATION_NAME,t)},module$exports$omid$common$serviceCommunication.startVerificationServiceCommunication=function(e,o){o=void 0===o?module$exports$omid$common$DetectOmid.isOmidPresent:o;var t=[],n=module$contents$omid$common$serviceCommunication_getValueForKeypath(e,module$contents$omid$common$serviceCommunication_EXPORTED_SERVICE_WINDOW_NAME);return n&&t.push(n),t.push((0,module$exports$omid$common$windowUtils.resolveTopWindowContext)(e)),module$contents$omid$common$serviceCommunication_startServiceCommunicationFromCandidates(e,t,module$contents$omid$common$serviceCommunication_EXPORTED_VERIFICATION_COMMUNICATION_NAME,o)};var module$exports$omid$common$VastProperties=function(e,o,t,n){this.isSkippable=e,this.skipOffset=o,this.isAutoPlay=t,this.position=n};module$exports$omid$common$VastProperties.prototype.toJSON=function(){return{isSkippable:this.isSkippable,skipOffset:this.skipOffset,isAutoPlay:this.isAutoPlay,position:this.position}};var module$exports$omid$common$version={ApiVersion:"1.0",Version:"1.3.2"},module$contents$omid$verificationClient$VerificationClient_VERIFICATION_CLIENT_VERSION=module$exports$omid$common$version.Version,module$contents$omid$verificationClient$VerificationClient_EventCallback;function module$contents$omid$verificationClient$VerificationClient_getThirdPartyOmid(){var e=module$exports$omid$common$OmidGlobalProvider.omidGlobal.omid3p;return e&&"function"==typeof e.registerSessionObserver&&"function"==typeof e.addEventListener?e:null}var module$exports$omid$verificationClient$VerificationClient=function(e){(this.communication=e||(0,module$exports$omid$common$serviceCommunication.startVerificationServiceCommunication)((0,module$exports$omid$common$windowUtils.resolveGlobalContext)()))?this.communication.onMessage=this.handleMessage_.bind(this):(e=module$contents$omid$verificationClient$VerificationClient_getThirdPartyOmid())&&(this.omid3p=e),this.remoteIntervals_=this.remoteTimeouts_=0,this.callbackMap_={},this.imgCache_=[],this.injectionId_=(e=module$exports$omid$common$OmidGlobalProvider.omidGlobal.omidVerificationProperties)?e.injectionId:void 0};module$exports$omid$verificationClient$VerificationClient.prototype.isSupported=function(){return!(!this.communication&&!this.omid3p)},module$exports$omid$verificationClient$VerificationClient.prototype.registerSessionObserver=function(e,o){(0,module$exports$omid$common$argsChecker.assertFunction)("functionToExecute",e),this.omid3p?this.omid3p.registerSessionObserver(e,o,this.injectionId_):this.sendMessage_("addSessionListener",e,o,this.injectionId_)},module$exports$omid$verificationClient$VerificationClient.prototype.addEventListener=function(e,o){(0,module$exports$omid$common$argsChecker.assertTruthyString)("eventType",e),(0,module$exports$omid$common$argsChecker.assertFunction)("functionToExecute",o),this.omid3p?this.omid3p.addEventListener(e,o):this.sendMessage_("addEventListener",o,e)},module$exports$omid$verificationClient$VerificationClient.prototype.sendUrl=function(e,o,t){(0,module$exports$omid$common$argsChecker.assertTruthyString)("url",e),module$exports$omid$common$OmidGlobalProvider.omidGlobal.document&&module$exports$omid$common$OmidGlobalProvider.omidGlobal.document.createElement?this.sendUrlWithImg_(e,o,t):this.sendMessage_("sendUrl",(function(e){e&&o?o():!e&&t&&t()}),e)},module$exports$omid$verificationClient$VerificationClient.prototype.sendUrlWithImg_=function(e,o,t){var n=this,i=module$exports$omid$common$OmidGlobalProvider.omidGlobal.document.createElement("img");function r(e){var o=n.imgCache_.indexOf(i);0<=o&&n.imgCache_.splice(o,1),e&&e()}this.imgCache_.push(i),i.addEventListener("load",r.bind(this,o)),i.addEventListener("error",r.bind(this,t)),i.src=e},module$exports$omid$verificationClient$VerificationClient.prototype.injectJavaScriptResource=function(e,o,t){var n=this;(0,module$exports$omid$common$argsChecker.assertTruthyString)("url",e),module$exports$omid$common$OmidGlobalProvider.omidGlobal.document?this.injectJavascriptResourceUrlInDom_(e,o,t):this.sendMessage_("injectJavaScriptResource",(function(i,r){i?(n.evaluateJavaScript_(r,e),o()):(module$exports$omid$common$logger.error("Service failed to load JavaScript resource."),t())}),e)},module$exports$omid$verificationClient$VerificationClient.prototype.injectJavascriptResourceUrlInDom_=function(e,o,t){var n=module$exports$omid$common$OmidGlobalProvider.omidGlobal.document,i=n.body;(n=n.createElement("script")).onload=o,n.onerror=t,n.src=e,n.type="application/javascript",i.appendChild(n)},module$exports$omid$verificationClient$VerificationClient.prototype.evaluateJavaScript_=function(a,b){try{eval(a)}catch(e){module$exports$omid$common$logger.error('Error evaluating the JavaScript resource from "'+b+'".')}},module$exports$omid$verificationClient$VerificationClient.prototype.setTimeout=function(e,o){if((0,module$exports$omid$common$argsChecker.assertFunction)("functionToExecute",e),(0,module$exports$omid$common$argsChecker.assertPositiveNumber)("timeInMillis",o),this.hasTimeoutMethods_())return module$exports$omid$common$OmidGlobalProvider.omidGlobal.setTimeout(e,o);var t=this.remoteTimeouts_++;return this.sendMessage_("setTimeout",e,t,o),t},module$exports$omid$verificationClient$VerificationClient.prototype.clearTimeout=function(e){(0,module$exports$omid$common$argsChecker.assertPositiveNumber)("timeoutId",e),this.hasTimeoutMethods_()?module$exports$omid$common$OmidGlobalProvider.omidGlobal.clearTimeout(e):this.sendOneWayMessage_("clearTimeout",e)},module$exports$omid$verificationClient$VerificationClient.prototype.setInterval=function(e,o){if((0,module$exports$omid$common$argsChecker.assertFunction)("functionToExecute",e),(0,module$exports$omid$common$argsChecker.assertPositiveNumber)("timeInMillis",o),this.hasIntervalMethods_())return module$exports$omid$common$OmidGlobalProvider.omidGlobal.setInterval(e,o);var t=this.remoteIntervals_++;return this.sendMessage_("setInterval",e,t,o),t},module$exports$omid$verificationClient$VerificationClient.prototype.clearInterval=function(e){(0,module$exports$omid$common$argsChecker.assertPositiveNumber)("intervalId",e),this.hasIntervalMethods_()?module$exports$omid$common$OmidGlobalProvider.omidGlobal.clearInterval(e):this.sendOneWayMessage_("clearInterval",e)},module$exports$omid$verificationClient$VerificationClient.prototype.hasTimeoutMethods_=function(){return"function"==typeof module$exports$omid$common$OmidGlobalProvider.omidGlobal.setTimeout&&"function"==typeof module$exports$omid$common$OmidGlobalProvider.omidGlobal.clearTimeout},module$exports$omid$verificationClient$VerificationClient.prototype.hasIntervalMethods_=function(){return"function"==typeof module$exports$omid$common$OmidGlobalProvider.omidGlobal.setInterval&&"function"==typeof module$exports$omid$common$OmidGlobalProvider.omidGlobal.clearInterval},module$exports$omid$verificationClient$VerificationClient.prototype.handleMessage_=function(e,o){o=e.method;var t=e.guid;if(e=e.args,"response"===o&&this.callbackMap_[t]){var n=(0,module$exports$omid$common$ArgsSerDe.deserializeMessageArgs)(module$contents$omid$verificationClient$VerificationClient_VERIFICATION_CLIENT_VERSION,e);this.callbackMap_[t].apply(this,n)}"error"===o&&window.console&&module$exports$omid$common$logger.error(e)},module$exports$omid$verificationClient$VerificationClient.prototype.sendOneWayMessage_=function(e,o){for(var t=[],n=1;n<arguments.length;++n)t[n-1]=arguments[n];this.sendMessage_.apply(this,[e,null].concat($jscomp.arrayFromIterable(t)))},module$exports$omid$verificationClient$VerificationClient.prototype.sendMessage_=function(e,o,t){for(var n=[],i=2;i<arguments.length;++i)n[i-2]=arguments[i];this.communication&&(i=(0,module$exports$omid$common$guid.generateGuid)(),o&&(this.callbackMap_[i]=o),n=new module$exports$omid$common$InternalMessage(i,"VerificationService."+e,module$contents$omid$verificationClient$VerificationClient_VERIFICATION_CLIENT_VERSION,(0,module$exports$omid$common$ArgsSerDe.serializeMessageArgs)(module$contents$omid$verificationClient$VerificationClient_VERIFICATION_CLIENT_VERSION,n)),this.communication.sendMessage(n))},(0,module$exports$omid$common$exporter.packageExport)("OmidVerificationClient",module$exports$omid$verificationClient$VerificationClient)},gi=exports,"object"===(void 0===gi?"undefined":_typeof(gi))&&"string"!=typeof gi.nodeName?fi(ei,gi):function(){function e(o){for(var t in o)o.hasOwnProperty(t)&&(o[t]=e(o[t]));return Object.freeze(o)}for(ji in hi=["1.3.2"],fi(ei,gi={}),gi)gi.hasOwnProperty(ji)&&(null==Object.getOwnPropertyDescriptor(ei,ji)&&Object.defineProperty(ei,ji,{value:{}}),hi.forEach((function(o){if(null==Object.getOwnPropertyDescriptor(ei[ji],o)){var t=e(gi[ji]);Object.defineProperty(ei[ji],o,{get:function(){return t},enumerable:!0})}})))}()}).call(this,__webpack_require__(27))},function(e,o,t){"use strict";var n,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"===("undefined"==typeof window?"undefined":i(window))&&(n=window)}e.exports=n},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.getVerificationParamsFromSessionStart=o.extractRequiredParamsAndLog=o.isLoggingParamsValid=o.getSourceTypeFromLoggingParameters=o.getSourceIdFromLoggingParameters=o.getZoneFromLoggingParameters=o.getBidIdFromLoggingParameters=void 0;var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=t(29),r=t(30),s=t(11),m=t(2),c=function(e){if(e&&e.__esModule)return e;var o={};if(null!=e)for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(o[t]=e[t]);return o.default=e,o}(m);function a(e,o){return e[o]}var d=o.getBidIdFromLoggingParameters=function(e){return a(e,"bidId")},l=o.getZoneFromLoggingParameters=function(e){return a(e,"zone")};o.getSourceIdFromLoggingParameters=function(e){return a(e,"sourceid")},o.getSourceTypeFromLoggingParameters=function(e){return a(e,"sourcetype")},o.isLoggingParamsValid=function(e){if(!e)return!1;var o=d(e),t=l(e),n=(0,i.getTenantBasedOnSupplyFromLoggingParameters)(e),s=(0,r.getVFRDBasedOnSupplyFromLoggingParameters)(e);return!!(o&&t&&n&&s)},o.extractRequiredParamsAndLog=function(e,o,t){var n=d(e),m=l(e),a=(0,i.getTenantBasedOnSupplyFromLoggingParameters)(),u=(0,r.getVFRDBasedOnSupplyFromLoggingParameters)();(0,s.logToTungsten)(a,o,n,m,u,c.SSI_CSM_VERSION,t)},o.getVerificationParamsFromSessionStart=function(e){return e.type===m.SESSION_START_EVENT&&e.data&&"object"===n(e.data)&&e.data.verificationParameters?JSON.parse(e.data.verificationParameters):{}}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.getTenantBasedOnSupplyFromLoggingParameters=void 0;var n=t(2);o.getTenantBasedOnSupplyFromLoggingParameters=function(){return n.TUNGSTEN_TENANTS.shadowCsmEvent}},function(e,o,t){"use strict";Object.defineProperty(o,"__esModule",{value:!0}),o.getVFRDBasedOnSupplyFromLoggingParameters=void 0;var n=t(2);o.getVFRDBasedOnSupplyFromLoggingParameters=function(){return n.VFRD_CODES.DEFAULT_VFRD_CODE}}],i={},j.m=h,j.c=i,j.d=function(e,o,t){j.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:t})},j.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},j.t=function(e,o){if(1&o&&(e=j(e)),8&o)return e;if(4&o&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(j.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var n in e)j.d(t,n,function(o){return e[o]}.bind(null,n));return t},j.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return j.d(o,"a",o),o},j.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},j.p="",j(j.s=24);function j(e){if(i[e])return i[e].exports;var o=i[e]={i:e,l:!1,exports:{}};return h[e].call(o.exports,o,o.exports,j),o.l=!0,o.exports}var h,i}));