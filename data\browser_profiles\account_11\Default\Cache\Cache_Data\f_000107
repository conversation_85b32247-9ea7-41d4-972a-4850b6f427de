{"/15184186/241_time.is_billboard":["html",1,null,null,1,1,1,0,0,null,null,null,1,null,[138472858844],[6712954268],[5622899987],[3542637249],null,null,null,null,null,null,null,0,null,null,null,null,null,null,"AOrYGskRS0W4cQgJJO5j7-VbMOjRlt9VGaHlCFOzdXWmYGU7LefuTmt2aONIHW0cuWu02ir74m-dafyPxtXBsvePZ6pEcbHpXImBEMuTL-qmzDA1yQ","CK2P3v25_o4DFcgwRAgdP0Mm1g",null,null,null,null,null,null,null,null,null,null,null,null,null,null,"2",null,null,null,null,null,null,null,null,null,null,null,null,null,null,1]}
<!doctype html><html><head><script>var inDapIF=true,inGptIF=true;</script></head><body leftMargin="0" topMargin="0" marginwidth="0" marginheight="0"><script>window.dicnf = {};</script><script data-jc="42" data-jc-version="r20250807" data-jc-flags="[&quot;x%278446&#39;9efotm(&amp;20067;&gt;8&amp;&gt;`dopb/%&lt;1732261!=|vqc)!7201061?&#39;9efotm(&amp;20723;&gt;:&amp;&gt;`dopb/%&lt;1245;05!=nehu`/!361&lt;&lt;101!9abk{a($16463496&amp;&lt;qqvb/%&lt;1374236!=8(&amp;2042627:&amp;&gt;6x&quot;]">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var r=this||self;function aa(a,b){a:{var c=["CLOSURE_FLAGS"];for(var d=r,f=0;f<c.length;f++)if(d=d[c[f]],d==null){c=null;break a}c=d}a=c&&c[a];return a!=null?a:b};function ba(a){r.setTimeout(()=>{throw a;},0)};var ca=aa(748402147,aa(1,!0));function v(a){v[" "](a);return a}v[" "]=function(){};var da={},w=null;let ea=void 0;function x(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var y=x(),C=x("m_m",!0);const F=x("jas",!0);var fa;const ha=[];ha[F]=7;fa=Object.freeze(ha);var G={};function H(a,b){return b===void 0?a.g!==I&&!!(2&(a.j[F]|0)):!!(2&b)&&a.g!==I}const I={};const ia=BigInt(Number.MIN_SAFE_INTEGER),ja=BigInt(Number.MAX_SAFE_INTEGER);function ka(a){if(typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a};function ma(a){return a};function J(a,b,c,d){var f=d!==void 0;d=!!d;const e=[];var g=a.length;let k,h=**********,m=!1;const l=!!(b&64),n=l?b&128?0:-1:void 0;b&1||(k=g&&a[g-1],k!=null&&typeof k==="object"&&k.constructor===Object?(g--,h=g):k=void 0,!l||b&128||f||(m=!0,h=(na??ma)(h-n,n,a,k,void 0)+n));b=void 0;for(f=0;f<g;f++){let p=a[f];if(p!=null&&(p=c(p,d))!=null)if(l&&f>=h){const q=f-n;(b??(b={}))[q]=p}else e[f]=p}if(k)for(let p in k){a=k[p];if(a==null||(a=c(a,d))==null)continue;g=+p;let q;l&&!Number.isNaN(g)&&(q=g+n)<h? e[q]=a:(b??(b={}))[p]=a}b&&(m?e.push(b):e[h]=b);return e}function oa(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ia&&a<=ja?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[F]|0;return a.length===0&&b&1?void 0:J(a,b,oa)}if(a!=null&&a[C]===G)return K(a);return}return a}let na;function K(a){a=a.j;return J(a,a[F]|0,oa)};function pa(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[F]|0;if(ca&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&qa();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[F]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var f=d-1;d=c[f];if(d!=null&&typeof d==="object"&&d.constructor===Object){const e=b&128?0:-1;f-=e;if(f>=1024)throw Error("pvtlmt");for(const g in d){const k=+g;if(k<f)c[k+e]=d[g],delete d[g];else break}b=b&-8380417|(f&1023)<<13}}}a[F]=b|2112;return a} function qa(){if(ca)throw Error("carr");if(y!=null){var a=ea??(ea={});var b=a[y]||0;b>=5||(a[y]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",ba(a))}};function ra(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=sa(a,c,!1,b&&!(c&16)):(a[F]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[C]===G){c=a.j;const d=c[F]|0;H(a,d)||(d&2?b=!0:!(d&32)||d&4096?b=!1:(c[F]=d|2,a.g=I,b=!0),b?(a=new a.constructor(c),a.i=I):a=sa(c,d));return a}}function sa(a,b,c,d){d??(d=!!(34&b));a=J(a,b,ra,d);d=32;c&&(d|=2);b=b&8380609|d;a[F]=b;return a} function ta(a){if(a.g===I){var b=a.j;b=sa(b,b[F]|0);b[F]|=2048;a.j=b;a.g=void 0;a.i=void 0;b=!0}else b=!1;if(!b&&H(a,a.j[F]|0))throw Error();};function ua(a,b,c){ta(a);const d=a.j;va(d,d[F]|0,b,c);return a}function va(a,b,c,d){const f=c+-1;var e=a.length-1;if(e>=0&&f>=e){const g=a[e];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(f<=e)return a[f]=d,b;d!==void 0&&(e=(b??(b=a[F]|0))>>13&1023||536870912,c>=e?d!=null&&(a[e+-1]={[c]:d}):a[f]=d);return b}function L(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return ua(a,b,c)};var M=class{constructor(a){this.j=pa(a)}toJSON(){return K(this)}};M.prototype[C]=G;M.prototype.toString=function(){return this.j.toString()};var N=class{constructor(a,b=!1){this.key=a;this.defaultValue=b;this.valueType="boolean"}},wa=class{constructor(a){this.key=a;this.defaultValue=0;this.valueType="number"}};var xa=new N("45368259"),ya=new N("45357156",!0),za=new N("45350890"),Aa=new N("45414892"),Ba=new N("45620832"),Ca=new N("45648564"),Da=new wa("45711101"),Ea=new wa("45711102");const Fa=RegExp("ad\\\\.doubleclick\\\\.net/(ddm/trackimp|pcs/view)");var O=(a,b)=>a.substring(a.length-7)=="&adurl="?a.substring(0,a.length-7)+b+"&adurl=":a+b;function Ga(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Ha(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Ia(a=document){return a.createElement("img")};function P(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function Ja(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Ka(a,b,c=null,d=!1,f=!1){return Pa(a,b,c,d,f)}function Pa(a,b,c,d,f=!1){a.google_image_requests||(a.google_image_requests=[]);const e=Ia(a.document);if(c||d){const g=k=>{c&&c(k);if(d){k=a.google_image_requests;const h=Array.prototype.indexOf.call(k,e,void 0);h>=0&&Array.prototype.splice.call(k,h,1)}Ja(e,"load",g);Ja(e,"error",g)};P(e,"load",g);P(e,"error",g)}f&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e);return e} function Qa(a,b=!1){var c=window;if(c.fetch){const d={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};b&&(d.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?d.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:d.headers={"Attribution-Reporting-Eligible":"event-source"});return c.fetch(a,d)}return Ka(c,a,void 0,!1,b)};async function Ra(a){if(a.g!==a.A){a.g++;try{const b=Qa(a.v,a.u);if(b instanceof HTMLImageElement)return await new Promise((d,f)=>{b.addEventListener("load",()=>{d()});b.addEventListener("error",()=>{f(Error(""))})}),b;const c=await b;if(c.status===999)throw Error("");return c}catch(b){return await new Promise(c=>void setTimeout(c,a.o)),await Ra(a)}}}var Sa=class{constructor(a){this.g=0;this.v=a.v;this.u=a.u??!1;this.o=a.o??1E3;this.A=a.A??5}};let Ta=0;function Ua(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)};function Q(a,b){a=a.g[b.key];if(b.valueType==="proto"){try{const c=JSON.parse(a);if(Array.isArray(c))return c}catch(c){}return b.defaultValue}return typeof a===typeof b.defaultValue?a:b.defaultValue}var Va=class{constructor(){this.g={}}};function S(){Wa||(Wa=new Xa);return Wa}var Xa=class extends Va{constructor(){super();var a=Ua(Ta,document.currentScript);a=a&&a.getAttribute("data-jc-flags")||"";try{const b=JSON.parse(a)[0];a="";for(let c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^"\\u0003\\u0007\\u0003\\u0007\\b\\u0004\\u0004\\u0006\\u0005\\u0003".charCodeAt(c%10));this.g=JSON.parse(a)}catch(b){}}},Wa;var Ya=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function Za(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\\\d+(?:.|\\n)*)\\\\2"),"$1");b=a.replace(RegExp("\\n *","g"),"\\n");break a}catch(d){b=c;break a}b=void 0}return b};const $a=RegExp("^https?://(\\\\w|-)+\\\\.cdn\\\\.ampproject\\\\.(net|org)(\\\\?|/|$)");var ab=class{constructor(a,b){this.g=a;this.i=b}},bb=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let T=null;function cb(){const a=r.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function db(){const a=r.performance;return a&&a.now?a.now():null};var eb=class{constructor(a,b){var c=db()||cb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const U=r.performance,fb=!!(U&&U.mark&&U.measure&&U.clearMarks),V=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=fb){var b;a=window;if(T===null){T="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(T=(b=c.match(/\\bdeid=([\\d,]+)/))?b[1]:"")}catch(c){}}b=T;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function gb(a){a&&U&&V()&&(U.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),U.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function W(a,b){const c={};c[a]=b;return[c]}function hb(a,b,c,d,f){const e=[];Ha(a,(g,k)=>{(g=ib(g,b,c,d,f))&&e.push(`${k}=${g}`)});return e.join(b)} function ib(a,b,c,d,f){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const e=[];for(let g=0;g<a.length;g++)e.push(ib(a[g],b,c,d+1,f));return e.join(c[d])}}else if(typeof a==="object")return f||(f=0),f<2?encodeURIComponent(hb(a,b,c,d,f+1)):"...";return encodeURIComponent(String(a))}function jb(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.l.length-1} function kb(a,b){let c="https://pagead2.googlesyndication.com"+b,d=jb(a)-b.length;if(d<0)return"";a.g.sort((e,g)=>e-g);b=null;let f="";for(let e=0;e<a.g.length;e++){const g=a.g[e],k=a.i[g];for(let h=0;h<k.length;h++){if(!d){b=b==null?g:b;break}let m=hb(k[h],a.l,",$");if(m){m=f+m;if(d>=m.length){d-=m.length;c+=m;f=a.l;break}b=b==null?g:b}}}a="";b!=null&&(a=`${f}${"trn"}=${b}`);return c+a}var lb=class{constructor(){this.l="&";this.i={};this.m=0;this.g=[]}};var mb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\\\\\/?#]*)@)?([^\\\\\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\\\\\/?#]|$))?([^?#]+)?(?:\\\\?([^#]*))?(?:#([\\\\s\\\\S]*))?$");function nb(a,b,c,d){const f=c.length;for(;(b=a.indexOf(c,b))>=0&&b<d;){var e=a.charCodeAt(b-1);if(e==38||e==63)if(e=a.charCodeAt(b+f),!e||e==61||e==38||e==35)return b;b+=f+1}return-1}var ob=/#|$/; function pb(a){const b=a.search(ob);let c=nb(a,0,"ase",b);if(c<0)return null;let d=a.indexOf("&",c);if(d<0||d>b)d=b;return decodeURIComponent(a.slice(c+4,d!==-1?d:0).replace(/\\+/g," "))}var qb=/[?&]($|#)/; function rb(a,b){var c=a.search(ob),d=0,f;const e=[];for(;(f=nb(a,d,"nis",c))>=0;)e.push(a.substring(d,f)),d=Math.min(a.indexOf("&",f)+1||c,c);e.push(a.slice(d));a=e.join("").replace(qb,"$1");(b="nis"+(b!=null?"="+encodeURIComponent(String(b)):""))?(c=a.indexOf("#"),c<0&&(c=a.length),d=a.indexOf("?"),d<0||d>c?(d=c,f=""):f=a.substring(d+1,c),a=[a.slice(0,d),f,a.slice(c)],c=a[1],a[1]=b?c?c+"&"+b:b:c,b=a[0]+(a[1]?"?"+a[1]:"")+a[2]):b=a;return b};function sb(a,b,c,d){let f,e;try{a.g&&a.g.g?(e=a.g.start(b.toString(),3),f=c(),a.g.end(e)):f=c()}catch(g){c=!0;try{gb(e),c=a.D(b,new Ya(g,{message:Za(g)}),void 0,d)}catch(k){a.m(217,k)}if(c)window.console?.error?.(g);else throw g;}return f}function tb(a,b,c,d){var f=X;return(...e)=>sb(f,a,()=>b.apply(c,e),d)} var vb=class{constructor(a=null){this.C=Y;this.g=a;this.i=null;this.l=!1;this.D=this.m}m(a,b,c,d,f){f=f||"jserror";let e=void 0;try{const D=new lb;var g=D;g.g.push(1);g.i[1]=W("context",a);b.error&&b.meta&&b.id||(b=new Ya(b,{message:Za(b)}));g=b;if(g.msg){b=D;var k=g.msg.substring(0,512);b.g.push(2);b.i[2]=W("msg",k)}var h=g.meta||{};k=h;if(this.i)try{this.i(k)}catch(B){}if(d)try{d(k)}catch(B){}d=D;h=[h];d.g.push(3);d.i[3]=h;var m;if(!(m=q)){d=r;h=[];let B;k=null;do{var l=d;try{var n;if(n=!!l&&l.location.href!= null)b:{try{v(l.foo);n=!0;break b}catch(z){}n=!1}var p=n}catch{p=!1}p?(B=l.location.href,k=l.document&&l.document.referrer||null):(B=k,k=null);h.push(new bb(B||""));try{d=l.parent}catch(z){d=null}}while(d&&l!==d);for(let z=0,La=h.length-1;z<=La;++z)h[z].depth=La-z;l=r;if(l.location&&l.location.ancestorOrigins&&l.location.ancestorOrigins.length===h.length-1)for(p=1;p<h.length;++p){const z=h[p];z.url||(z.url=l.location.ancestorOrigins[p-1]||"",z.g=!0)}m=h}var q=m;let R=new bb(r.location.href,!1);m= null;const la=q.length-1;for(l=la;l>=0;--l){var t=q[l];!m&&$a.test(t.url)&&(m=t);if(t.url&&!t.g){R=t;break}}t=null;const yb=q.length&&q[la].url;R.depth!==0&&yb&&(t=q[la]);e=new ab(R,t);if(e.i){q=D;var u=e.i.url||"";q.g.push(4);q.i[4]=W("top",u)}var E={url:e.g.url||""};if(e.g.url){const B=e.g.url.match(mb);var A=B[1],Ma=B[3],Na=B[4];u="";A&&(u+=A+":");Ma&&(u+="//",u+=Ma,Na&&(u+=":"+Na));var Oa=u}else Oa="";A=D;E=[E,{url:Oa}];A.g.push(5);A.i[5]=E;ub(this.C,f,D,this.l,c)}catch(D){try{ub(this.C,f,{context:"ecmserr", rctx:a,msg:Za(D),url:e?.g.url??""},this.l,c)}catch(R){}}return!0}};class wb{};function ub(a,b,c,d=!1,f,e){if((d?a.g:Math.random())<(f||.01))try{let g;c instanceof lb?g=c:(g=new lb,Ha(c,(h,m)=>{var l=g;const n=l.m++;h=W(m,h);l.g.push(n);l.i[n]=h}));const k=kb(g,"/pagead/gen_204?id="+b+"&");k&&(typeof e!=="undefined"?Ka(r,k,e):Ka(r,k))}catch(g){}}function xb(){var a=Y,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var zb=class{constructor(){this.g=Math.random()}};let Y,X; const Z=new class{constructor(a,b){this.i=[];this.l=b||r;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.i=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=V()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new eb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;U&&V()&&U.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(db()||cb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;U&&V()&&U.mark(b);!this.g||this.i.length> 2048||this.i.push(a)}}}(1,window);function Ab(){window.google_measure_js_timing||(Z.g=!1,Z.i!==Z.l.google_js_reporting_queue&&(V()&&Array.prototype.forEach.call(Z.i,gb,void 0),Z.i.length=0))} (function(a){Y=a??new zb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());xb();X=new vb(Z);X.i=b=>{var c=Ta;c!==0&&(b.jc=String(c),c=(c=Ua(c,document.currentScript))&&c.getAttribute("data-jc-version")||"unknown",b.shv=c)};X.l=!0;window.document.readyState==="complete"?Ab():Z.g&&P(window,"load",()=>{Ab()})})();function Bb(a,b,c,d){return tb(a,b,c,d)} function Cb(a,b,c,d){var f=wb;var e="B";f.B&&f.hasOwnProperty(e)||(e=new f,f.B=e);f=[];!b.eid&&f.length&&(b.eid=f.toString());ub(Y,a,b,!0,c,d)};function Db(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b};function Eb(){const a=window;if(a.gmaSdk||a.webkit?.messageHandlers?.getGmaViewSignals)return a;try{const b=window.parent;if(b.gmaSdk||b.webkit?.messageHandlers?.getGmaViewSignals)return b}catch(b){}return null} function Fb(a,b={},c=()=>{},d=()=>{},f=200,e,g){const k=String(Math.floor(Ga()*2147483647));let h=0;const m=l=>{try{const n=typeof l.data==="object"?l.data:JSON.parse(l.data);k===n.paw_id&&(window.clearTimeout(h),window.removeEventListener("message",m),n.signal?c(n.signal):n.error&&d(n.error))}catch(n){g("paw_sigs",{msg:"postmessageError",err:n instanceof Error?n.message:"nonError",data:l.data==null?"null":l.data.length>500?l.data.substring(0,500):l.data})}};window.addEventListener("message",l=>{e(903, ()=>{m(l)})()});a.postMessage({paw_id:k,...b});h=window.setTimeout(()=>{window.removeEventListener("message",m);d("PAW GMA postmessage timed out.")},f)};function Gb(a=document){return!!a.featurePolicy?.allowedFeatures().includes("attribution-reporting")};var Hb=class extends M{};function Ib(a,b){return L(a,2,b)}function Jb(a,b){return L(a,3,b)}function Kb(a,b){return L(a,4,b)}function Lb(a,b){return L(a,5,b)}function Mb(a,b){return L(a,9,b)} function Nb(a,b){{var c=b;ta(a);const l=a.j;b=l[F]|0;if(c==null)va(l,b,10);else{var d=c===fa?7:c[F]|0,f=d,e=!!(2&d)&&!!(4&d)||!!(256&d),g=e||Object.isFrozen(c),k=!0,h=!0;for(let n=0;n<c.length;n++){var m=c[n];e||(m=H(m),k&&(k=!m),h&&(h=m))}e||(d=k?13:5,d=h?d&-4097:d|4096);g&&d===f||(c=[...c],f=0,d=2&b?d|2:d&-3,d&=-273);d!==f&&(c[F]=d);b=va(l,b,10,c);2&d||!(4096&d||16&d)||(c=l,b===void 0&&(b=c[F]|0),b&32&&!(b&4096)&&(c[F]=b|4096))}}return a}function Ob(a,b){return ua(a,11,b==null?b:ka(b))} function Pb(a,b){return L(a,1,b)}function Qb(a,b){return ua(a,7,b==null?b:ka(b))}var Rb=class extends M{};const Sb="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Tb(){var a=window;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Sb).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} function Ub(a){return Ob(Nb(Lb(Ib(Pb(Kb(Qb(Mb(Jb(new Rb,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new Hb;c=L(c,1,b.brand);return L(c,2,b.version)})||[]),a.wow64||!1)}function Vb(){return Tb()?.then(a=>Ub(a))??null};class Wb{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};window.viewReq=[];function Xb(a,b){b?(b=Ia(),b.src=a.replace("&amp;","&"),b.attributionSrc="",window.viewReq.push(b)):(b=new Image,b.src=a.replace("&amp;","&"),window.viewReq.push(b))} function Yb(a,b){const c={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};b&&(c.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?c.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:c.headers={"Attribution-Reporting-Eligible":"event-source"});fetch(a,c).catch(()=>{Xb(a,b)})}function Zb(a,b){if(Q(S(),Ea)){const c=S();Ra(new Sa({v:a,u:b,o:Q(c,Da),A:Q(c,Ea)}))}else window.fetch?Yb(a,b):Xb(a,b)} function $b(){const a=r.document;return new Promise(b=>{const c=Db(a);if(c){var d=()=>{(a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]??0)!==3&&(Ja(a,c,d),b())};P(a,c,d)}})}Ta=42; window.vu=a=>{var b=Q(S(),ya)||Q(S(),Aa);const c=Eb();if(b&&c?.gmaSdk?.getViewSignals){var d=c.gmaSdk.getViewSignals();d&&!Q(S(),Aa)&&(a=O(a,"&ms="+d))}Q(S(),xa)&&"__google_lidar_radf_"in window&&(a=O(a,"&avradf=1"));const f=[];d=()=>{const k=new Wb;f.push(k.promise);return k.resolve};if(Q(S(),Ca)){var e=$b();if(e!=null){const k=d();e.then(()=>{a=O(a,"&sbtr=1");k()})}}Q(S(),Ba)&&(a=O(a,"&sbtr=1"));if(Q(S(),za)&&(e=Vb(),e!=null)){const k=d();e.then(h=>{var m=JSON.stringify(K(h));h=[];var l=0;for(var n= 0;n<m.length;n++){var p=m.charCodeAt(n);p>255&&(h[l++]=p&255,p>>=8);h[l++]=p}m=3;m===void 0&&(m=0);if(!w)for(w={},l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),n=["+/=","+/","-_=","-_.","-_"],p=0;p<5;p++){var q=l.concat(n[p].split(""));da[p]=q;for(var t=0;t<q.length;t++){var u=q[t];w[u]===void 0&&(w[u]=t)}}m=da[m];l=Array(Math.floor(h.length/3));n=m[64]||"";for(p=q=0;q<h.length-2;q+=3){var E=h[q],A=h[q+1];u=h[q+2];t=m[E>>2];E=m[(E&3)<<4|A>>4];A=m[(A&15)<<2|u>>6];u=m[u& 63];l[p++]=t+E+A+u}t=0;u=n;switch(h.length-q){case 2:t=h[q+1],u=m[(t&15)<<2]||n;case 1:h=h[q],l[p]=m[h>>2]+m[(h&3)<<4|t>>4]+u+n}h=l.join("");h.length>0&&(a=O(a,"&uach="+h));k()})}if(b&&c?.webkit?.messageHandlers?.getGmaViewSignals){const k=d();Fb(c.webkit.messageHandlers.getGmaViewSignals,{},h=>{Q(S(),Aa)||(a=O(a,"&"+h));k()},()=>{k()},200,Bb,Cb)}const g=pb(a)===(2).toString()||Fa.test(a);g&&(b=Gb(window.document)?6:5,a=rb(a,b));f.length>0?Promise.all(f).then(()=>{Zb(a,g)}):Zb(a,g)};}).call(this);</script><script>vu("https://securepubads.g.doubleclick.net/pcs/view?xai\\x3dAKAOjsuBFxREGjVi6eRDJ6hQawvtKZncNpmv8DJrCOpMvFLOnQtxa9AKtXJwsiUN6Jy9kqwaC9aebrBrNUK9EnVdRVjOC_NRu_cQgXJIQhPYnLw0kHeX_ABIr9f59N0M7ectpEycjn2EEFy8h7eOxH4ZKCwMfjZTjLe-rttS1jrA2zdHj8a9Tm1D6s560LYMpgq6I24-1AGRQusVlLaG35hDMoYWsvYtCq4qMhPdLN5THy4l1oROgI-U8kzBV8gSSzEvgBhm_j56nG5u5XYIgU_NOz1ScHyv9qzjR8ZdaexSCHpSdtS5X-1aj9p2OhGSbTn2RyNJTImcA6amig4CZxuR0_D8CWGYlfZDuDCMtaIJt-y4XbtWtS_naRF6FfDAOMaHyfZ0wkbgkyF6w8d6rC0Nee97aQg-RyF6ajsOislZBDbSteVW_4ewfw\\x26sai\\x3dAMfl-YScdvLb52qgvE4Oq8g42VjUfS2uWzOjRWWqhogVJcJKHP7aXTxjHFs9jWHircRPGENqTIpxFUGr351fgctNE-ONPRIuYI0wmsfulbWRZlxqzH2QzYntje8Gm4l81FVBIoylUBUo3TW2MJay6smJd2t-4py9Mx6ZuwuzeQYmFIOmJ81HV5lObIVUTOUiDQ9MyAqdv6BrOv8b3cz_4lgrX73W0EqrrQbLApDG1njTTzk3CHduKTrdqkgEKXKaLNk8Gb0\\x26sig\\x3dCg0ArKJSzNS8YF7AfRvfEAE\\x26uach_m\\x3d%5BUACH%5D\\x26urlfix\\x3d1\\x26adurl\\x3d")</script><style>div{margin:0;padding:0;}.abgc{display:block;height:15px;position:absolute;right:17px;top:1px;text-rendering:geometricPrecision;z-index:2147483646;}.abgb{display:inline-block;height:15px;}.abgc,.jar .abgc,.jar .cbb{opacity:1;}.abgc{cursor:pointer;}.cbb{cursor:pointer;height:15px;width:15px;z-index:2147483646;background-color:#ffffff;opacity:0;}.cbb svg{position:absolute;top:0;right:0;height:15px;width:15px;stroke:;fill:#00aecd;stroke-width:1.25;}.cbb:hover{cursor:pointer;}.abgb{position:absolute;right:0px;top:0px;}.cbb{position:absolute;right:1px;top:1px;}.abgs{display:none;height:100%;}.abgl{text-decoration:none;}.abgs svg,.abgb svg{display:inline-block;height:15px;width:auto;vertical-align:top;}.abgc .il-wrap{background-color:#ffffff;height:15px;white-space:nowrap;}.abgc .il-wrap.exp{border-bottom-left-radius:5px;}.abgc .il-text,.abgc .il-icon{display:inline-block;}.abgc .il-text{padding-right:1px;padding-left:5px;height:15px;width:96px;}.abgc .il-icon{height:15px;width:15px;}.abgc .il-text svg{fill:#000000;}.abgc .il-icon svg{fill:#00aecd}</style><div id="abgc" class="abgc" dir="ltr"><div id="abgb" class="abgb"><div class="il-wrap"><div class="il-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15"><path d="M7.5 1.5a6 6 0 100 12 6 6 0 100-12m0 1a5 5 0 110 10 5 5 0 110-10zM6.625 11h1.75V6.5h-1.75zM7.5 3.75a1 1 0 100 2 1 1 0 100-2z"/></svg></div></div></div><div id="abgs" class="abgs"><a id="abgl" class="abgl" href="https://adssettings.google.com/whythisad?source=display&amp;reasons=ASp5-QATUWrMTvtQ7jLotKSqByIo1FB1c-81nPHGsooXF9ZFQ0NVvXg_EO0X2Ko3pc5WE-S0jB6GjKo7LyoTGdxIrC9I-WRXd2jB2X0TGIdu5GSglvilweUB-rSiZ7ICAdDuEweLl2aVoAi5PhdJNv722YmWPjxkwn2-oKzPAxXh_uUkDv-nhHWMNc2KC0CM0rb689oEILzdHeq8qyQG7x_O8FZFoSF78sHe2e0hDg29VMaCcvnd78LWRDRuCzDmd-DI1byCVNbMVQ-pwyrEMPlSl1UzhV2poitZKz92kqo4TUbv5-3665Qt7zk4uS5x4n_vSFCiVgnc9QdkUx5igDvovqHbUjQwOnM2GE__U6tQ_t4HjrNO1rRYBMoEIYBzVvV2XKcln3F-GWR3eRD3fSjPs90kzyGiP1_6zGxszh0gD1yaAmFITbtXNQu-eGp8okZDqMg1Q6c8CvjUulyRuWDVgUZwIYKxzOsq7PlxgCysY29JpQFQmHpl4rpJIIv9hUNzTHpsDfCRZxk7GTUmrnjqViCzNwFzoRiT4saIdVcEFnlThzln2K3_rJ-f7TbGzntR02g3FsC4-5lnU6v8JbL7HGjs3LcHdO6iwNDbPni-MZIejf9t90Sxgm9ntr2qkxj2mDfH0wkZz9_PbjmZTuK4VWs4UihqZejbNI67s6lrjFsG-t3z9ecp3w4oGvVbyUe4-fc4oEhWSz671gHZ5c9VIA2-ZIi2yenqZ_fVB3U1LRPIQczrhEGX9Aha5PmI-5TzBYW6OdMoZKXOSOx6mlPekj-kuhe1cRbDtXHHv0QG1PZc_HUwBxuWzk-KI5qTgLD5LBBL8J0pHrbupxdiuCKo-ZDsYIE3pGT1WZKLjnbkMLBUvZmH5fm5Vz8Ymd7okcf6ZFjyM-7-vtsJTjxih5VxVNUAeCPK-xl3EIGcY9ZXi_XZx3__1WJ3A8QErV5SM5Af9vXe-vRXEP0HP44SMIdt6IOVZeO30wpoA8CT3kvahckZyRH0PlQDZ0Yyf1QFiTUUQB2GA_PkFylhaknXNCle-euNHm_UAysU1hMrgicSzGitjhdCE2dD02NU4CbcK4zYO43l1rUMW0itb0ny8Fm1E6DGsewSlaBwMKD4wKjEhqlhC7S1mkiaKqulYg1gGDyRzHRIfZ8TXd7GxBdaV1x_ChdC4ELHrFw9HFB7WbQ8TnHgcZ5zvMjnOcvDFwZKP0uoCag9oDONipeyAeRDMhMa&amp;opi=122715837" target="_blank"><div class="il-wrap exp"><div class="il-text"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 103 16"><path d="M8.57 8.44v3.19q-1.63 1.34-3.55 1.34-1.98 0-3.19-1.2Q.62 10.56.62 8.57q0-1.22.5-2.27.49-1.06 1.46-1.63.96-.58 2.35-.58 1.45 0 **********.63 1.18 1.9l-1.02.29q-.24-.93-.86-1.39-.62-.47-1.63-.47-1.55 0-2.34.95-.79.95-.79 2.49 0 1.14.38 ********** 1.14 ********** 1.64.39 1.34 0 2.5-.89V9.47H4.93V8.44h3.64zm4.1-1.97q1.26 0 **********.82.83 2.33 0 1.84-.9 2.59-.9.76-2.02.76-1.18 0-2.05-.78-.86-.78-.86-2.47 0-1.64.83-2.45.84-.8 2.08-.8zm0 5.64q.9 0 1.37-.67.47-.67.47-1.76 0-1.17-.53-1.76-.53-.59-1.31-.59-.8 0-1.32.6t-.52 1.79q0 1.18.53 ********** 1.31.61zm6.67-5.64q1.26 0 **********.82.83 2.33 0 1.84-.9 2.59-.89.76-2.02.76-1.18 0-2.04-.78-.87-.78-.87-2.47 0-1.64.84-2.45.84-.8 2.07-.8zm0 5.64q.9 0 1.37-.67.47-.67.47-1.76 0-1.17-.53-1.76-.53-.59-1.31-.59-.8 0-1.32.6-.51.6-.51 1.79 0 1.18.52 ********** 1.31.61zm9.24-5.5v5.38q0 1.32-.27 1.98-.26.65-.93 1.01-.68.37-1.62.37-1.05 0-1.75-.47-.71-.47-.71-1.54l1.03.16q.06.49.41.74.36.24 1.01.24.82 0 1.2-.33t.47-.81q.08-.48.08-1.33-.68.82-1.72.82-1.22 0-1.95-.9-.74-.9-.74-2.26 0-1.37.72-2.29.71-.91 1.98-.91 1.1 0 1.79.88h.02v-.74h.98zm-2.7 5.35q.67 0 1.19-.53.52-.54.52-1.8 0-1.11-.5-1.71-.49-.59-1.23-.59-.75 0-1.22.61t-.47 1.67q0 1.22.5 1.79.49.56 1.21.56zm5.33-7.72v8.59h-1.05V4.24h1.05zm7.02 5.74h-4.66q.06 1.04.59 1.58.52.55 1.31.55.6 0 1.01-.32.41-.32.63-.97l1.08.14q-.26.97-.97 1.49-.7.52-1.75.52-1.4 0-2.19-.86-.79-.85-.79-2.34 0-1.47.76-2.39.77-.91 2.16-.91.68 0 1.31.3t1.07 1.01q.44.72.44 2.2zm-4.6-.86h3.51q-.06-.95-.59-1.37-.52-.42-1.14-.42-.75 0-1.23.5t-.55 1.29zM46.6 5.19v.88h-1.3v2.15l1.14-.5-.03.96-1.11.5v3.48q0 .37-.33.68-.33.31-1.72.24l-.28-.87.91.05q.51.03.51-.52V9.49q-.91.39-1.18.47l-.61-.77q1.77-.55 1.79-.6V6.07h-1.52v-.88h1.52V2.91h.91v2.28h1.3zm5.65-1.83v4.19H47V3.36h5.25zm-4.34 1.62h3.43v-.74h-3.43v.74zm0 1.69h3.43v-.82h-3.43v.82zm5.75 5.48l-.36.94-2.49-.02q-2.01 0-3.3-1.12-.51 1.19-1.44 1.74l-.62-.67q.83-.53 1.21-1.32.38-.8.33-2.24l.89.11q0 .8-.1 1.43.37.51 1.46.95V9h-2.73v-.88h6.68V9h-3.04v1.04h2.37v.88h-2.37v1.23q.55.08 1.29.08.42 0 2.22-.08zM57.5 2.7l.85.39q-.21.68-.7 1.76l-.49 1.06v7.74h-.92V7.4q-.28.47-.97 1.21l-.78-.55q1.95-2.02 3.01-5.36zm7.75 6.02v.88h-7.49v-.88h1.7V6.09h-1.35v-.91h1.35V2.84h.9v2.34h2.22V2.84h.95v2.34h1.39v.91h-1.39v2.63h1.72zm-4.89 0h2.22V6.09h-2.22v2.63zm-.17 1.32l.78.51q-.99 1.83-2.68 3.11l-.7-.66q1.48-1.11 2.6-2.96zm5.23 3.05l-.76.54q-.93-1.67-2.77-2.97l.77-.53q1.83 1.34 2.76 2.96zm7.95-8.29h3.24q-.05 4.85-.46 6.76-.42 1.92-1.48 1.92-.48 0-1.87-.4l-.16-1.01q1.35.5 1.89.5.51 0 .83-1.89.32-1.89.34-5.05h-2.56q-.45 1.26-1.39 2.51l-.62-.4v5.36h-.95v-.78h-1.99v.97h-.95V4.98h1.1q.49-.86.74-2.11l.86.35q-.34 1.27-.65 1.76h1.84v2.44q1.33-1.88 1.8-4.65l.96.22q-.16.71-.52 1.81zm-5.18 3.37h1.99V5.85h-1.99v2.32zm0 3.27h1.99V9.05h-1.99v2.39zm6.79-1.18l-.86.38q-.53-1.42-1.63-2.82l.74-.49q1.21 1.39 1.75 2.93zm13.95-5.57v.88H81.2v1.65q0 1.93-.11 2.89-.11.96-.48 1.81-.37.85-1.31 1.64l-.68-.63q1.7-1.11 1.7-4.27V4.69h3.81V2.95h.9v1.74h3.9zm12.1 2.65v.88h-10.2v-.88h4.75V5.53h-2.43q-.57 1-1.26 1.54l-.76-.61q1.34-1 1.85-3.13l.89.22q-.07.31-.35 1.1h2.06V2.84h.92v1.81h3.39v.88H96.5v1.81h4.53zm-1.56 1.54v4.75h-.91v-.85h-5.21v.85h-.91V8.88h7.03zm-6.12 3.02h5.21V9.76h-5.21v2.14z"/></svg></div><div class="il-icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 15"><path d="M7.5 1.5a6 6 0 100 12 6 6 0 100-12m0 1a5 5 0 110 10 5 5 0 110-10zM6.625 11h1.75V6.5h-1.75zM7.5 3.75a1 1 0 100 2 1 1 0 100-2z"/></svg></div></div></a></div></div><div id="cbb" class="cbb" tabindex="0" role="button"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_2157_481)"><path fill="#fff" d="M15 0v15H0V0z"/><path fill="#fff" d="M15 0v15H0V0z"/><circle cx="7.5" cy="11.5" r="1.5" transform="rotate(-180 7.5 11.5)" fill="#00aecd"/><circle cx="7.5" cy="7.5" r="1.5" transform="rotate(-180 7.5 7.5)" fill="#00aecd"/><circle cx="7.5" cy="3.5" r="1.5" transform="rotate(-180 7.5 3.5)" fill="#00aecd"/></g><defs><clipPath id="clip0_2157_481"><path fill="#fff" transform="rotate(90 7.5 7.5)" d="M0 0h15v15H0z"/></clipPath></defs></svg></div><style>.mute_panel{z-index:2147483646;}.abgac{position:absolute;left:0px;top:0px;z-index:2147483646;display:none;width:100%;height:100%;background-color:#FAFAFA;}.mlsc{height:100%;display:flex;justify-content:center;align-items:center;}.mls{animation:mlskf 2s linear infinite;height:50%;width:50%;}.mlsd{stroke-dasharray:1,189;stroke-dashoffset:0;animation:mlsdkf 1.4s ease-in-out infinite;}@keyframes mlskf{100%{transform:rotate(360deg);}}@keyframes mlsdkf{0%{stroke-dasharray:1,189;stroke-dashoffset:0;}50%{stroke-dasharray:134,189;stroke-dashoffset:-53px;}100%{stroke-dasharray:134,189;stroke-dashoffset:-188px;}}</style><div id="mute_panel" class="mute_panel" aria-hidden="true"><div id="abgac" class="abgac" aria-hidden="true"><div id="mlsc" class="mlsc"><svg class="mls" viewBox="50 50 100 100"><circle class="mlsd" cx="100" cy="100" r="30" fill="none" stroke="#9E9E9E" stroke-width="3"/></svg></div></div></div><script data-jc="60" src="https://tpc.googlesyndication.com/pagead/js/r20250807/r20110914/abg_lite_fy2021.js" async data-jc-version="r20250807" data-jcp-attribution-data="[[null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png&quot;,null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png&quot;,&quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai=BNXwDKqCXaK3SOsjhkPIPv4aZsQ3yp4fdRgAAABABINqJvCo4AVjcyYDtgwRg5QOyAQd0aW1lLmlzugEJZ2ZwX2ltYWdlyAEJ2gEQaHR0cHM6Ly90aW1lLmlzL5gC3gLAAgLgAgDqAh8vMTUxODQxODYvMjQxX3RpbWUuaXNfYmlsbGJvYXJk-AKB0h6QA7AJmAOsAqgDAeAEAdIFBhCcm_6AGZAGAaAGIKgHuL6xAqgH89EbqAeW2BuoB6qbsQKoB5oGqAf_nrECqAffn7ECqAf4wrECqAf7wrECqAfn17EC2AcA4AcB0ggpCIBhEAEYnQEyAooCOg2AQIDAgICAgKiAAqgDSL39wTpYvNrd_bn-jgPYCAKACgWYCwGADAGqDQJNWNoNEwjdqN_9uf6OAxXIMEQIHT9DJtbqDRMIyube_bn-jgMVyDBECB0_QybW0BUB-BYBgBcBshkBNQ\\u0026sigh=6bzS6y06PFY\\u0026cid=CAQSnQEA2abss39W6DioP6djIe3qHZMBRSm3xVH_h2aIefqHw0FeZ5SQL9lPRXcfLVLVXgKsi0WBxFGVf-njwnpNglxz8f6iplIylmuZbjDyOjIElYpqPibN1X0mBQTYBDEODuUKs6fM7i5mCJSRVfC7PYF8K4UlV_1VT0-QQU2sW_xbAZ2o_p-DG5bwnLS0V3Br61EuyUYsOimYqXazg5nc&quot;,&quot;SCvM3AWwqWMIABDBtaGZDRgAIgBCF2NhLXB1Yi04MDYxOTQ2NDEzNTM3OTg0SAJYIHABuAHcyYDtgwQ&quot;,[&quot;user_feedback_menu_interaction&quot;,&quot;&quot;,0],null,null,null,null,&quot;此广告有什么问题？&quot;,null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/back_blue.png&quot;,&quot;感谢您的反馈！&quot;,&quot;我们将对此广告进行审核，以便改善用户在今后的体验。&quot;,&quot;感谢您的反馈！&quot;,&quot;我们会根据您的反馈审核此网站上的广告。&quot;,null,null,null,&quot;即将关闭广告：%1$d 秒&quot;,null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/abg_blue.png&quot;,&quot;https://www.google.com/url?ct=abg\\u0026q=https://www.google.com/adsense/support/bin/request.py%3Fcontact%3Dabg_afc%26url%3Dhttps://time.is/%26gl%3DMX%26hl%3Dzh%26client%3Dca-pub-8061946413537984%26ai0%3DBNXwDKqCXaK3SOsjhkPIPv4aZsQ3yp4fdRgAAABABINqJvCo4AVjcyYDtgwRg5QOyAQd0aW1lLmlzugEJZ2ZwX2ltYWdlyAEJ2gEQaHR0cHM6Ly90aW1lLmlzL5gC3gLAAgLgAgDqAh8vMTUxODQxODYvMjQxX3RpbWUuaXNfYmlsbGJvYXJk-AKB0h6QA7AJmAOsAqgDAeAEAdIFBhCcm_6AGZAGAaAGIKgHuL6xAqgH89EbqAeW2BuoB6qbsQKoB5oGqAf_nrECqAffn7ECqAf4wrECqAf7wrECqAfn17EC2AcA4AcB0ggpCIBhEAEYnQEyAooCOg2AQIDAgICAgKiAAqgDSL39wTpYvNrd_bn-jgPYCAKACgWYCwGADAGqDQJNWNoNEwjdqN_9uf6OAxXIMEQIHT9DJtbqDRMIyube_bn-jgMVyDBECB0_QybW0BUB-BYBgBcBshkBNQ\\u0026usg=AOvVaw0bQ8rQHusMWPnpqHU_j-ZY&quot;,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png&quot;,0,[[&quot;发送反馈&quot;,[&quot;user_feedback_menu_option&quot;,&quot;1&quot;,1],[&quot;此广告有什么问题？&quot;,[[&quot;已多次看到此广告&quot;,[&quot;mute_survey_option&quot;,&quot;2&quot;,1]],[&quot;广告内容不当&quot;,[&quot;mute_survey_option&quot;,&quot;8&quot;,1]],[&quot;对此广告不感兴趣&quot;,[&quot;mute_survey_option&quot;,&quot;7&quot;,1]],[&quot;广告遮挡内容&quot;,[&quot;mute_survey_option&quot;,&quot;3&quot;,1]]]],[&quot;user_feedback_undo&quot;,&quot;1&quot;,1]]],[&quot;https://googleads.g.doubleclick.net/pagead/images/adchoices/iconx2-000000.png&quot;,&quot;广告选择&quot;,&quot;%1$s 已关闭此广告&quot;,null,&quot;https://www.gstatic.com/images/branding/googlelogo/2x/googlelogo_dark_color_84x28dp.png&quot;,&quot;发送反馈&quot;,&quot;非常感谢！反馈有助于改进 Google 广告&quot;,null,null,null,&quot;https://googleads.g.doubleclick.net/pagead/images/abg/iconx2-000000.png&quot;,&quot;Google 提供的广告&quot;,null,&quot;查看我的 Google 广告设置&quot;,null,&quot;https://www.gstatic.com&quot;,&quot;&quot;,&quot;%1$s 投放的广告&quot;,&quot;广告设置&quot;,&quot;https://adssettings.google.com&quot;,null,null,null,0,null,null,null,0,1],&quot;APyvJYwAAAPlW1tbW10sW251bGwsImh0dHBzOi8vZ29vZ2xlYWRzLmcuZG91YmxlY2xpY2submV0L3BhZ2VhZC9pbnRlcmFjdGlvbi8_YWk9Qk5Yd0RLcUNYYUszU09zamhrUElQdjRhWnNRM3lwNGZkUmdBQUFCQUJJTnFKdkNvNEFWamN5WUR0Z3dSZzVRT3lBUWQwYVcxbExtbHp1Z0VKWjJad1gybHRZV2RseUFFSjJnRVFhSFIwY0hNNkx5OTBhVzFsTG1sekw1Z0MzZ0xBQWdMZ0FnRHFBaDh2TVRVeE9EUXhPRFl2TWpReFgzUnBiV1V1YVhOZlltbHNiR0p2WVhKay1BS0IwaDZRQTdBSm1BT3NBcWdEQWVBRUFkSUZCaENjbV82QUdaQUdBYUFHSUtnSHVMNnhBcWdIODlFYnFBZVcyQnVvQjZxYnNRS29CNW9HcUFmX25yRUNxQWZmbjdFQ3FBZjR3ckVDcUFmN3dyRUNxQWZuMTdFQzJBY0E0QWNCMGdncENJQmhFQUVZblFFeUFvb0NPZzJBUUlEQWdJQ0FnS2lBQXFnRFNMMzl3VHBZdk5yZF9ibi1qZ1BZQ0FLQUNnV1lDd0dBREFHcURRSk5XTm9ORXdqZHFOXzl1ZjZPQXhYSU1FUUlIVDlESnRicURSTUl5dWJlX2JuLWpnTVZ5REJFQ0IwX1F5YlcwQlVCLUJZQmdCY0JzaGtCTlFcdTAwMjZzaWdoPTZielM2eTA2UEZZXHUwMDI2Y2lkPUNBUVNuUUVBMmFic3MzOVc2RGlvUDZkakllM3FIWk1CUlNtM3hWSF9oMmFJZWZxSHcwRmVaNVNRTDlsUFJYY2ZMVkxWWGdLc2kwV0J4RkdWZi1uanducE5nbHh6OGY2aXBsSXlsbXVaYmpEeU9qSUVsWXBxUGliTjFYMG1CUVRZQkRFT0R1VUtzNmZNN2k1bUNKU1JWZkM3UFlGOEs0VWxWXzFWVDAtUVFVMnNXX3hiQVoyb19wLURHNWJ3bkxTMFYzQnI2MUV1eVVZc09pbVlxWGF6ZzVuYyIsbnVsbCxudWxsLG51bGwsMSwiU0N2TTNBV3dxV01JQUJEQnRhR1pEUmdBSWdCQ0YyTmhMWEIxWWkwNE1EWXhPVFEyTkRFek5UTTNPVGcwU0FKWUlIQUJ1QUhjeVlEdGd3USIsIjM1NDI2MzcyNDkiXV1dLFsxLDEsMSwxLDEsbnVsbCxudWxsLG51bGwsbnVsbCw1LDEsbnVsbCxmYWxzZSxudWxsLDFdLFtudWxsLG51bGwsIk1YIl0sbnVsbCxudWxsLFsxMjI3MTU4MzddXR0NExDrGsQsJ_52q_MKWPBrJGqo32OrtQLMg5TKkToa6dJT7ZhRODytRfADbbLm67Bg76erT2KjpsMYZBfkZ9uUpUleVTy12CvTQlRptITm-o-nYMRG4wxWBzEy6doQWzNbmx4gIEnXfsrmzPRZbZQa0oayjUEB1vcnuE12R8IXQW9USHsekaT6z1y58EuTIFxTHDc0r9e7XqorCnTACe1cx5T6j7-twMBKJr_ZThUjQp9fplpDXBP-HhgncYTnb8RkI5ekJxae2_fBkWWsNU6FeKyLNYnZ-get4R-Tu5Pd4B174NbuG_36zfLI4bSENtIpY1bcTWyBIfUJxvpkcFM,qBmo2KPu6x5EV82qsEN4CA&quot;,&quot;https://adssettings.google.com/whythisad?source=display\\u0026reasons=ASp5-QATUWrMTvtQ7jLotKSqByIo1FB1c-81nPHGsooXF9ZFQ0NVvXg_EO0X2Ko3pc5WE-S0jB6GjKo7LyoTGdxIrC9I-WRXd2jB2X0TGIdu5GSglvilweUB-rSiZ7ICAdDuEweLl2aVoAi5PhdJNv722YmWPjxkwn2-oKzPAxXh_uUkDv-nhHWMNc2KC0CM0rb689oEILzdHeq8qyQG7x_O8FZFoSF78sHe2e0hDg29VMaCcvnd78LWRDRuCzDmd-DI1byCVNbMVQ-pwyrEMPlSl1UzhV2poitZKz92kqo4TUbv5-3665Qt7zk4uS5x4n_vSFCiVgnc9QdkUx5igDvovqHbUjQwOnM2GE__U6tQ_t4HjrNO1rRYBMoEIYBzVvV2XKcln3F-GWR3eRD3fSjPs90kzyGiP1_6zGxszh0gD1yaAmFITbtXNQu-eGp8okZDqMg1Q6c8CvjUulyRuWDVgUZwIYKxzOsq7PlxgCysY29JpQFQmHpl4rpJIIv9hUNzTHpsDfCRZxk7GTUmrnjqViCzNwFzoRiT4saIdVcEFnlThzln2K3_rJ-f7TbGzntR02g3FsC4-5lnU6v8JbL7HGjs3LcHdO6iwNDbPni-MZIejf9t90Sxgm9ntr2qkxj2mDfH0wkZz9_PbjmZTuK4VWs4UihqZejbNI67s6lrjFsG-t3z9ecp3w4oGvVbyUe4-fc4oEhWSz671gHZ5c9VIA2-ZIi2yenqZ_fVB3U1LRPIQczrhEGX9Aha5PmI-5TzBYW6OdMoZKXOSOx6mlPekj-kuhe1cRbDtXHHv0QG1PZc_HUwBxuWzk-KI5qTgLD5LBBL8J0pHrbupxdiuCKo-ZDsYIE3pGT1WZKLjnbkMLBUvZmH5fm5Vz8Ymd7okcf6ZFjyM-7-vtsJTjxih5VxVNUAeCPK-xl3EIGcY9ZXi_XZx3__1WJ3A8QErV5SM5Af9vXe-vRXEP0HP44SMIdt6IOVZeO30wpoA8CT3kvahckZyRH0PlQDZ0Yyf1QFiTUUQB2GA_PkFylhaknXNCle-euNHm_UAysU1hMrgicSzGitjhdCE2dD02NU4CbcK4zYO43l1rUMW0itb0ny8Fm1E6DGsewSlaBwMKD4wKjEhqlhC7S1mkiaKqulYg1gGDyRzHRIfZ8TXd7GxBdaV1x_ChdC4ELHrFw9HFB7WbQ8TnHgcZ5zvMjnOcvDFwZKP0uoCag9oDONipeyAeRDMhMa\\u0026opi=122715837&quot;,&quot;为什么显示此广告？&quot;,1,0],null,null,0,null,0,0,1,0,0,0,0,0,0,0,null,0,1,0,null,[[&quot;post_click_menu_height_when_bottom_anchor_on_mobile&quot;,&quot;0&quot;],[&quot;show_ad_after_mute&quot;,&quot;true&quot;],[&quot;jake_ui_extension&quot;,&quot;jake_default_ui&quot;]],1,1,0,null,null,0,null,null,&quot;right&quot;,0,null,&quot;r20250807/r20110914&quot;,null,0,null,0]"></script><div class="GoogleActiveViewInnerContainer"id="avic_CK2P3v25_o4DFcgwRAgdP0Mm1g"style="left:0px;top:0px;width:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;"></div><div style="display:inline"class="GoogleActiveViewElement"data-google-av-cxn="https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjsuEe6MYQfODxLApXN2sLiUsIQPEcqRjBkwBVBO1ww63j4wXOKXoMvDYyDIQLQAvGLXwYG2fdN5tnYL68MxPSm-MozPpERU0nuGB64AUEsVCfdQztdHGTn8pRaNosQ2ZM-SUn8A21sb8f8x2CPkz0tDARGgiXD5MIQrlI05JcWXdEqenyYM&amp;sig=Cg0ArKJSzC6VGDmi8xF5EAE"data-google-av-adk="3215004302"data-google-av-metadata="la=0&amp;xdi=0&amp;"data-google-av-ufs-integrator-metadata="CooBCj9tb2RlbF9wZXJzb25fY291bnRyeV9jb2RlX01YX3BlcnNvbl9yZWdpb25fY29kZV80ZDU4MmQ0NDQ2Lmpzb24SGkNLMlAzdjI1X280REZjZ3dSQWdkUDBNbTFnGAEiEQi0EyC0EygCMAI4AV3NzEw-KJWdyNX4_____wEwlZ3IVTgDQAFIAFABEpkCCowCaHR0cHM6Ly9wYWdlYWQyLmdvb2dsZXN5bmRpY2F0aW9uLmNvbS9wY3MvYWN0aXZldmlldz94YWk9QUtBT2pzdUVlNk1ZUWZPRHhMQXBYTjJzTGlVc0lRUEVjcVJqQmt3QlZCTzF3dzYzajR3WE9LWG9NdkRZeURJUUxRQXZHTFh3WUcyZmRONXRuWUw2OE14UFNtLU1velBwRVJVMG51R0I2NEFVRXNWQ2ZkUXp0ZEhHVG44cFJhTm9zUTJaTS1TVW44QTIxc2I4Zjh4MkNQa3owdERBUkdnaVhENU1JUXJsSTA1SmNXWGRFcWVueVlNJnNpZz1DZzBBcktKU3pDNlZHRG1pOHhGNUVBRRIAGgAgASgAMAQaHgoaQ0syUDN2MjVfbzRERmNnd1JBZ2RQME1tMWcQBQ"data-google-av-override="-1"data-google-av-dm="2"data-google-av-aid="0"data-google-av-naid="1"data-google-av-slift=""data-google-av-cpmav=""data-google-av-btr="https://securepubads.g.doubleclick.net/pcs/view?xai=AKAOjstuOhQ8A94gOT6D4WOSFmegEivrJzK85rSz9UCCoBwDurlPnD_ynqVtlbmKFP6necJdU5-1X0GB7h_rbQfgkUUnXgFJPH2BdHyNmcI2mGwDKqgOoVliBK1CaOWxgnhPNeiZApP048mkGT9I8iosTdDpTDdtAeeCpk3F7eZ0jgGFFwi_Suvdno_41PsX-oBfvzqtpeDCIpsS5HizpaG0S3CWkrjfBX2hr9a7bojMnyeXbvTZO9ahWRYVYGuERMLEo3ILTC0eEVToKo2N01VLH-6Wc2Wp67Mm5d-FAlsDIRzd6PelZK3P8t2RxsixlYXn7T7Id_EyIZN_KVudOAE6J0m-mGtENieGuzgjKy06kWI_XRGTdPlDOa1derDdwVlFYGKsSzP6W7OdZ1IlelQYkyf9jWXvrWahN5M-3AduVIVu8tk8kGXl1hQJ&amp;sai=AMfl-YRR-jm50Lwsrhxc1w6ROQ-Hxkcr7VId632wv6HAPXS5plgBnsoznyhduVyYzlmvmEZ65ef-2HezOm-FzWafUJLxsPGEto0U5pOgkEv6tlAMksIMnPdJ9GIPo5dCvJxyOsVp0S3Xim9fO1GFeXgfAZBxuV4QHzLdyaEmXDpBwYw4Jn1-Pt0LIuoopfoxi-r0Qw8vY52EDZ5DWOZfC6eDsd6jx71jPCReAWAczzkx6LR5MhycBSVwCPmULq27dLdPQ6k&amp;sig=Cg0ArKJSzCinnkfh7US1EAE&amp;uach_m=%5BUACH%5D&amp;urlfix=1&amp;adurl="data-google-av-itpl="19"data-google-av-rs="4"data-google-av-flags="[&quot;x%278440&#39;9efotm(&amp;753374%2bejvf/%27844&gt;&#39;9wuvb$&amp;56533&gt;!=|vqc)!273794&amp;&lt;qqvb/%&lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;&lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&#39;76463;21$?ebkpb$&amp;0366717&gt;*&gt;bgipf+!3=712363%9aihwc)!7202&lt;217&#39;9efotm(&amp;20061;48&amp;&gt;`dopb/%&lt;1707200!=8(&amp;2005575?&amp;&gt;`dopb/%&lt;170642?!=|vqc)!7201;=50&#39;9wuvb$&amp;03641654*&gt;bgipf+!3=731103%9aihwc)!7200?073&#39;9efotm(&amp;2004?51;&amp;&gt;`dopb/%&lt;17&gt;474&gt;!=nehu`/!36406412!9abk{a($167745;=&amp;&lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&gt;7&amp;&lt;qqvb/%&lt;104=460!=nehu`/!363;42&gt;7!9abk{a($1656;3?&lt;&amp;&lt;cbotf+*01011776%2bejvf/%72&gt;17266!=efdwa*&#39;7616?=&lt;=$?ebkpb$&amp;0335225&gt;*&gt;bgipf+!3=340764%94&gt;44653~&quot;]"><!-- Freestar Universal Creative Info: Bidder: ix, BID: 703d02af6ccac088, BWH: 300x250 -->\n<script src = "https://a.pub.network/core/puc/banner.js"></script>\n<script>\n  var ucTagData = {};\n  ucTagData.adServerDomain = "";\n  ucTagData.pubUrl = "https://time.is/";\n  ucTagData.targetingMap = {"amznactt":["OPEN"],"amznbid":["13it2io"],"amzniid":["JFpkDf0a7t7S2Wr32ADBtTAAAAGYkEml9wYAAAJYAQBhcHNfdHhuX2JpZDEgICBhcHNfdHhuX2ltcDEgICDrS_HG"],"amznp":["9wieww"],"amznsz":["300x250"],"custom_bidder_size":["ix_300x250"],"floors_hour":["19"],"floors_id":["e2b310"],"floors_rtt":["12"],"floors_user":["0"],"freestar_domain":["time.is"],"freestar_path":["/"],"fs-auuid":["7b1fbaaf-414a-46fe-9eca-31c58db7f5a3"],"fs_ad_product":["display"],"fs_adid":["703d02af6ccac088"],"fs_auction_id":["7e641bb6-4b3c-4106-8839-b4d9984e0061"],"fs_bidder":["ix"],"fs_clientservermask":["2303232201230320010200002"],"fs_format":["banner"],"fs_liveintent":["Y"],"fs_pageview_id":["3500b341995c7e8ffac4561d9b14e4e4"],"fs_pb":["0.35"],"fs_placementname":["time.is_billboard"],"fs_session_id":["17d2f28c-7949-46ae-8810-2324ab3b5e65"],"fs_size":["300x250"],"fs_source":["client"],"fs_testgroup":["optimised"],"fs_uuid":["387f09c4-3022-4159-af49-24385486a0b7"],"fs_version":["6.129.0"],"fsitf":["Y-YNY-NYYY-Y--Y---YY--------------------"],"fspbg":["fs_universal"],"fsrebid":["0"],"fsrefresh":["0"],"user-agent":["Chrome"]};\n  ucTagData.hbPb = "0.35";\n  ucTagData.hbFormat = "banner";\n  ucTagData.adId = "703d02af6ccac088";\n  // if you're using GAM and want to track outbound clicks on native ads you can add this line\n  ucTagData.clickUrlUnesc = "https://adclick.g.doubleclick.net/pcs/click?xai=AKAOjssOe7rIov8aQxyp-ZYKO1vPjvp4bF4dwipAuQfawA1kF49Yd6P1rltmhiobDLNvKgcv28MPMZ4LvLPUBFgwXrb8o5LZSROADwsGknS9EC_McJWqbQKTekT9e04g25vtGG0jlGeOuyCQSMv95FncXNoaIWDNsjt-w9zQ96Sn49X4bQ1LlSX2Zak9RD3HiXfYspKHhQP5Gm1KHMQCqLRYJ7y3IioBHVlp__P8HrXH7Lr6k5WD8n412RyVcMOCaOWsQyYmVE_xiL62bqVRJeXcanbjWYHjNlNJqCoM-JPzKYd-0hceFPYxf40TrhA19V4uke0ff0M9gAwK1aEk6jQb5-fGcOOllhTZVWXnR5JiPOwAupGmlyiIYXweE06OYx69l81o9G7i7TSOqor79g_IGVTr8auR&sai=AMfl-YSrer37IA2AuKlYtnnxyf3IrN8L5G8Qm727ILd1nbIdgmPCCK7rk0B_EgkgreeaMBD8etgJEK2SKdKSJuxHQdszSCj7_AzL29Lb2qHu5tjwsHiL7QFTgZzHhL1wBoEhdnC6Df_hf03bKUKmU2uLntqSJ79kZeJlNWfU3R-0trclsHnD74LX-xDvMHB2HqiN3PxxYekUQLm7J_MOgkYI7HlD57jjGm-n0_61tY-C3QxE6NKr5haoLXnvh26mtFoa5yE&sig=Cg0ArKJSzLzG3qJ3NrYiEAE&fbs_aeid=%5Bgw_fbsaeid%5D&urlfix=1&adurl=";\n  ucTagData.requestAllAssets = true;\n\n  try {\n    ucTag.renderAd(document, ucTagData);\n  } catch (e) {\n    console.log(e);\n  }\n</script></div><script id="googleActiveViewDisplayScript" src="https://pagead2.googlesyndication.com/pagead/managed/js/activeview/current/ufs_web_display.js"></script><script type="text/javascript">osdlfm();</script><div style="bottom:0;right:0;width:100px;height:100px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAAB6SURBVBjTbZABDsUgDELxBtz/tIsVkP2/ZrE2RfZa4DvWicm3AjhdguQ+VY3unsql2ylV+n9qeUsnb5NYK5IKsriHNldELWD6yZAsfUI5/4gpDwl0jnD6JsL6wcictTMrZX2nRBHV7pCl1wYtf+/b3pmxvQs7vALe9weSvQWz7XIZlgAAAABJRU5ErkJggg==') !important;"></div><script data-jc="103" data-jc-version="r20250807" data-jcp-base_url="https://googleads.g.doubleclick.net/pagead/conversion/?ai=&amp;sigh=BpnfxIaauQU" data-jcp-cpu_label="heavy_ad_intervention_cpu" data-jcp-net_label="heavy_ad_intervention_network">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function n(a,b){a:{var e=["CLOSURE_FLAGS"];for(var c=h,d=0;d<e.length;d++)if(c=c[e[d]],c==null){e=null;break a}e=c}a=e&&e[a];return a!=null?a:b};function p(a){h.setTimeout(()=>{throw a;},0)};var u=n(748402147,n(1,!0));let v=void 0;function w(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var x=w(),y=w("m_m",!0);const z=w("jas",!0);var A={};function B(a,b){return b===void 0?a.h!==C&&!!(2&(a.g[z]|0)):!!(2&b)&&a.h!==C}const C={};const D=BigInt(Number.MIN_SAFE_INTEGER),E=BigInt(Number.MAX_SAFE_INTEGER);function F(a){return a};function G(a,b,e,c){var d=c!==void 0;c=!!c;const l=[];var f=a.length;let g,k=**********,J=!1;const r=!!(b&64),q=r?b&128?0:-1:void 0;b&1||(g=f&&a[f-1],g!=null&&typeof g==="object"&&g.constructor===Object?(f--,k=f):g=void 0,!r||b&128||d||(J=!0,k=(H??F)(k-q,q,a,g,void 0)+q));b=void 0;for(d=0;d<f;d++){let m=a[d];if(m!=null&&(m=e(m,c))!=null)if(r&&d>=k){const t=d-q;(b??(b={}))[t]=m}else l[d]=m}if(g)for(let m in g){a=g[m];if(a==null||(a=e(a,c))==null)continue;f=+m;let t;r&&!Number.isNaN(f)&&(t=f+q)<k?l[t]= a:(b??(b={}))[m]=a}b&&(J?l.push(b):l[k]=b);return l}function I(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=D&&a<=E?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[z]|0;return a.length===0&&b&1?void 0:G(a,b,I)}if(a!=null&&a[y]===A)return K(a);return}return a}let H;function K(a){a=a.g;return G(a,a[z]|0,I)};function L(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[z]|0;if(u&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&M();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[z]=b|2048),a;var e=a;b|=64;var c=e.length;if(c){var d=c-1;c=e[d];if(c!=null&&typeof c==="object"&&c.constructor===Object){const l=b&128?0:-1;d-=l;if(d>=1024)throw Error("pvtlmt");for(const f in c){const g=+f;if(g<d)e[g+l]=c[f],delete c[f];else break}b=b&-8380417|(d&1023)<<13}}}a[z]=b|2112;return a} function M(){if(u)throw Error("carr");if(x!=null){var a=v??(v={});var b=a[x]||0;b>=5||(a[x]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",p(a))}};function N(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var e=a[z]|0;a.length===0&&e&1?a=void 0:e&2||(!b||4096&e||16&e?a=O(a,e,!1,b&&!(e&16)):(a[z]|=34,e&4&&Object.freeze(a)));return a}if(a!=null&&a[y]===A){e=a.g;const c=e[z]|0;B(a,c)||(c&2?b=!0:c&32&&!(c&4096)?(e[z]=c|2,a.h=C,b=!0):b=!1,b?(a=new a.constructor(e),a.m=C):a=O(e,c));return a}}function O(a,b,e,c){c??(c=!!(34&b));a=G(a,b,N,c);c=32;e&&(c|=2);b=b&8380609|c;a[z]=b;return a};function P(a,b,e){if(e!=null&&typeof e!=="string")throw Error();if(a.h===C){var c=a.g;c=O(c,c[z]|0);c[z]|=2048;a.g=c;a.h=void 0;a.m=void 0;c=!0}else c=!1;if(!c&&B(a,a.g[z]|0))throw Error();a=a.g;a:{var d=a[z]|0;c=b+-1;const l=a.length-1;if(l>=0&&c>=l){const f=a[l];if(f!=null&&typeof f==="object"&&f.constructor===Object){f[b]=e;break a}}c<=l?a[c]=e:e!==void 0&&(d=(d??a[z]|0)>>13&1023||536870912,b>=d?e!=null&&(a[d+-1]={[b]:e}):a[c]=e)}};var Q=class{constructor(a){this.g=L(a)}toJSON(){return K(this)}};Q.prototype[y]=A;Q.prototype.toString=function(){return this.g.toString()};var R=class extends Q{};function S(a=window){return a};var T=/#|$/;const U=function(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)}(103,document.currentScript);if(U==null)throw Error("JSC not found 103");const V={},W=U.attributes;for(let a=W.length-1;a>=0;a--){const b=W[a].name;b.indexOf("data-jcp-")===0&&(V[b.substring(9)]=W[a].value)} (function(a,b,e){var c=window;a&&b&&e&&c.ReportingObserver&&c.fetch&&(new c.ReportingObserver((d,l)=>{d=d[0];if(d?.body?.id==="HeavyAdIntervention"){d=(d.body.message?.indexOf("network")||0)>0?e:b;var f=a.search(T);var g;b:{for(g=0;(g=a.indexOf("ad_signals",g))>=0&&g<f;){var k=a.charCodeAt(g-1);if(k==38||k==63)if(k=a.charCodeAt(g+10),!k||k==61||k==38||k==35)break b;g+=11}g=-1}k=g;if(k<0)f=null;else{g=a.indexOf("&",k);if(g<0||g>f)g=f;f=decodeURIComponent(a.slice(k+11,g!==-1?g:0).replace(/\\+/g," "))}f? (navigator.sendBeacon("https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&label="+d),d={i:f,label:d},f=new R,d!=null&&(d.i!=null&&P(f,1,d.i),d.u!=null&&P(f,3,d.u),d.label!=null&&P(f,6,d.label),d.l!=null&&P(f,7,d.l),d.j!=null&&P(f,8,d.j),d.o!=null&&P(f,11,d.o)),S(h).fence?.reportEvent({eventType:"interaction",eventData:JSON.stringify(K(f)),destination:["buyer"]})):c.fetch(`${a}&label=${d}`,{keepalive:!0,method:"get",mode:"no-cors"});l.disconnect()}},{types:["intervention"], buffered:!0})).observe()})(V.base_url,V.cpu_label,V.net_label);}).call(this);</script></body></html>
{"/15184186/time.is_970x90_728x90_320x50_sticky":["html",0,null,null,0,50,320,1,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,0,null,null,null,null,null,null,"AOrYGslXqzWxZnMwa10HhsoG5C0a","CK6P3v25_o4DFcgwRAgdP0Mm1g",null,null,null,null,null,null,null,null,null,null,null,null,null,null,"3",null,null,null,null,null,null,null,null,null,null,null,null,null,null,1]}

