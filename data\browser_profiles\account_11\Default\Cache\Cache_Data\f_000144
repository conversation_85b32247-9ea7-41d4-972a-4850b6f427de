window.amzncsm=window.amzncsm||{},window.amzncsm.rmR||function(){var startCsmComp,amzncsm=amzncsm||{},AAX_TYPE_MACRO="${AAX_TYPE}",AAX_PAYLOAD_MACRO="${AAX_PAYLOAD}",AAX_PIXEL_TYPE_MAP={"v/":"v","atf/":"atf","btr/":"btr","bnmk/":"other","":"other"},PIXEL_SHORT_NAME_MAP={btr:"bt",atf:"at",v:"v",adCsm:"ac",adViewability:"av",other:"o"};amzncsm.getEncodedPayload=function(a){try{var b=JSON.parse(a);b.ver=amzncsm.version,a=JSON.stringify(b),a=a.replaceAll("%","<per>"),a=encodeURIComponent(a)}catch(c){}return a},amzncsm.sanitizePixelMap=function(a){amzncsm.pixelParams.pm={};try{var b=a||{};amzncsm.pixelParams.pm="object"==typeof b?b:JSON.parse(b)}catch(c){amzncsm.log(c)}},amzncsm.sanitizePixelUrl=function(a,b){return a?(/^https?:\/\//.test(a)===!1&&(a=document.location.protocol+"//"+a),b&&"/"!==a.substr(a.length-1)&&(a+="/"),"https:"===document.location.protocol&&/^http:\/\//.test(a)===!0&&(a=a.replace("http://","https://")),a):a},amzncsm.replacePixelMacro=function(a,b,c){var d,e=encodeURIComponent(b);if(-1!==a.indexOf(e))return a.replace(e,c);if(-1!==a.indexOf(b))return a.replace(b,c);throw d=b+" is not part of URL "+a,new Error(d.replace(/\$/g,""))},amzncsm.constructInstrUrl=function(a,b,c){var d="?";return void 0!==c&&""!==c?a+=c:(a+="?p=",d="&"),a=a+b+d+"cb="+Math.round(1e7*Math.random())},amzncsm.constructMacroBasedUrl=function(a,b,c){var d=amzncsm.sanitizePixelUrl(a,!1),e=AAX_PIXEL_TYPE_MAP[c];return d=amzncsm.replacePixelMacro(d,AAX_TYPE_MACRO,e),d=amzncsm.replacePixelMacro(d,AAX_PAYLOAD_MACRO,b),d=d+"&cb="+Math.round(1e7*Math.random())},amzncsm.constructPixelUrl=function(a,b,c){if(void 0===c)throw new Error("URL Key is undefined");return"instrUrl"===c||"iu"===c?amzncsm.constructInstrUrl(amzncsm.pixelParams[c],a,b):amzncsm.constructMacroBasedUrl(amzncsm.pixelParams[c],a,b)},amzncsm.getPixelUrls=function(a,b,c){var d,e=PIXEL_SHORT_NAME_MAP[b],f=amzncsm.pixelParams.pm||{},g=[],h=f[e]||["instrUrl"];for(d=0;d<h.length;d+=1)g.push(amzncsm.constructPixelUrl(a,c,h[d]));return g},amzncsm.getVuePixelType=function(a,b){var c=AAX_PIXEL_TYPE_MAP[b],d=JSON.parse(a);return"other"===c&&(void 0!==d.adCsm?c="adCsm":void 0!==d.adViewability&&(c="adViewability")),c},amzncsm.sendPixel=function(a,b,c){var d,e,f,g;if(!a||0===a.length)throw new Error("No payload to send");for(d=amzncsm.getVuePixelType(a,b),e=amzncsm.getEncodedPayload(a),f=amzncsm.getPixelUrls(e,d,b),g=0;g<f.length;g+=1)null!=f[g]&&(c?navigator.sendBeacon(f[g],""):(new Image).src=f[g])},amzncsm=amzncsm||{},amzncsm.errorCodes={SLOT_INSIDE_UNFRIENDLY_FRAME:1,UNSUPPORTED_BROWSER:2,ERROR_SENDING_PIXEL:3,INVALID_API:4,MALFORMED_URL:5},amzncsm.errors=[],amzncsm.errorMessages=[],amzncsm.exceptions={},amzncsm.reportErrors=function(){var a,b;for(a=0;a<amzncsm.errors.length;a++)-1===amzncsm.errorMessages.indexOf(amzncsm.errors[a])&&(amzncsm.errorMessages.push(amzncsm.errors[a]),b='{"adViewability":[{"error": {"m": "'+amzncsm.errors[a]+'"}}], "c": "viewability", "api": "'+amzncsm.api+'", "error": 1}',amzncsm.sendPixel(b,""));amzncsm.errors=[],amzncsm.detachAllHandlers&&amzncsm.detachAllHandlers()},function(a){function b(a){var b,c,d,e,f,g;return a?(b=a.indexOf("?"),c=a.split(":"),d=c.length,-1!==b||d>=2?(e=c[d-2],f=c[d-1],g=a.slice(0,b)+k+e+k+f,g.replace(/^\s+|\s+$/g,"").replace(/%/g,"<per>").slice(-h)):a.replace(/^\s+|\s+$/g,"").slice(-h)):a}function c(a){if(a&&a.s){var b,c=a.s.length>0?a.s[0]:"",d=a.s.length>1?a.s[1]:"";c&&(b=c.match(j)),b&&3===b.length||!d||(b=d.match(i)),b&&3===b.length&&(a.f=b[1],a.l=b[2])}}function d(a,d){if(d=d||{},!a)return{};a.m&&a.m.message&&(a=a.m);var i,j,k,l,m,n={m:e(a,d),c:a.c?""+a.c:a.c,s:[],l:a.l||a.line||a.lineno||a.lineNumber,name:a.name,type:a.type},o=0,p=0;if(i=a.stack||(a.err?a.err.stack:""),i&&i.split)for(j=i.split("\n");o<j.length&&n.s.length<g;)k=j[o++],k&&n.s.unshift(b(k));else for(l=f(a.args||arguments,"callee"),o=0,p=0;l&&g>o;)m=h,l.skipTrace||(k=l.toString(),k&&k.substr&&(m=0===p?4*h:m,m=1===p?2*h:m,n.s.unshift(k.substr(0,m)),p++)),l=f(l,"caller"),o++;return!n.f&&n.s.length>0&&c(n),n}function e(a,b){var c=b.m||b.message||"";return c+=a.m&&a.m.message?a.m.message:a.m&&a.m.target&&a.m.target.tagName?"Error handler invoked by "+a.m.target.tagName+" tag":a.m?a.m:a.message?a.message:"Unknown error"}function f(a,b){try{return a[b]}catch(c){return""}}var g=5,h=256,i=/\(?([^\s]*):(\d+):\d+\)?/,j=/.*@(.*):(\d*)/,k=":";a.constructErrorMessage=d}(amzncsm),amzncsm.exceptions.invalidModuleException=function(a){this.value=a,this.message="does not conform to the expected format of a module",this.toString=function(){return this.value+this.message}},amzncsm.exceptions.invalidStringException=function(a){this.value=a,this.message="should be a string. Pixel Queue expects a string.",this.toString=function(){return this.value+this.message}},amzncsm.getModules=function(a,b){return{eventUtils:{"default":!0,params:[],global:!0},utils:{"default":!0,params:[],global:!0},pixelQueue:{"default":!0,params:[]},viewDuration:{params:[]}}},function(window){var CustomEvent,s,_marks,_marksIndex,_filterEntries,_clearEntries,_slice=Array.prototype.slice;try{_slice.call(document.documentElement)}catch(e){Array.prototype.slice=function(a,b){var c,d,e,f,g,h;if(b="undefined"!=typeof b?b:this.length,"[object Array]"===Object.prototype.toString.call(this))return _slice.call(this,a,b);if(d=[],f=this.length,g=a||0,g=g>=0?g:f+g,h=b?b:f,0>b&&(h=f+b),e=h-g,e>0)if(d=new Array(e),this.charAt)for(c=0;e>c;c++)d[c]=this.charAt(g+c);else for(c=0;e>c;c++)d[c]=this[g+c];return d}}Object.keys||(Object.keys=function(){"use strict";var a=Object.prototype.hasOwnProperty,b=!{toString:null}.propertyIsEnumerable("toString"),c=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],d=c.length;return function(e){if("object"!=typeof e&&("function"!=typeof e||null===e))throw new TypeError("Object.keys called on non-object");var f,g,h=[];for(f in e)a.call(e,f)&&h.push(f);if(b)for(g=0;d>g;g++)a.call(e,c[g])&&h.push(c[g]);return h}}()),window.JSON||(window.JSON={parse:function(sJSON){return eval("("+sJSON+")")},stringify:function(){var a=Object.prototype.toString,b=Array.isArray||function(b){return"[object Array]"===a.call(b)},c={'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t"},d=function(a){return c[a]||"\\u"+(a.charCodeAt(0)+65536).toString(16).substr(1)},e=/[\\"\u0000-\u001F\u2028\u2029]/g;return function f(c){var g,h,i,j;if(null==c)return"null";if("number"==typeof c)return isFinite(c)?c.toString():"null";if("boolean"==typeof c)return c.toString();if("object"==typeof c){if("function"==typeof c.toJSON)return f(c.toJSON());if(b(c)){for(g="[",h=0;h<c.length;h++)g+=(h?", ":"")+f(c[h]);return g+"]"}if("[object Object]"===a.call(c)){i=[];for(j in c)c.hasOwnProperty(j)&&i.push(f(j)+": "+f(c[j]));return"{"+i.join(", ")+"}"}}return'"'+c.toString().replace(e,d)+'"'}}()}),Array.isArray||(Array.isArray=function(a){return"[object Array]"===Object.prototype.toString.call(a)}),String.prototype.endsWith||(String.prototype.endsWith=function(a,b){var c,d=this.toString();return("number"!=typeof b||!isFinite(b)||Math.floor(b)!==b||b>d.length)&&(b=d.length),b-=a.length,c=d.indexOf(a,b),-1!==c&&c===b}),window.Event&&(CustomEvent=function(a,b){b=b||{bubbles:!1,cancelable:!1,detail:void 0};var c=document.createEvent("CustomEvent");return c.initCustomEvent(a,b.bubbles,b.cancelable,b.detail),c},CustomEvent.prototype=window.Event.prototype,window.CustomEvent=CustomEvent),window.performance||(window.performance={}),window.performance.now||(window.performance.now=performance.now||performance.webkitNow||performance.msNow||performance.mozNow),window.performance.now||(s=Date.now?Date.now():+new Date,performance.timing&&performance.timing&&(s=performance.timing.navigationStart),window.performance.now=function(){var a=Date.now?Date.now():+new Date;return a-s}),_marks=[],_marksIndex={},_filterEntries=function(a,b){for(var c=0,d=_marks.length,e=[];d>c;c++)_marks[c][a]==b&&e.push(_marks[c]);return e},_clearEntries=function(a,b){for(var c,d=_marks.length;d--;)c=_marks[d],c.entryType!=a||void 0!==b&&c.name!=b||_marks.splice(d,1)},window.performance.mark||(window.performance.mark=window.performance.webkitMark||function(a){var b={name:a,entryType:"mark",startTime:window.performance.now(),duration:0};_marks.push(b),_marksIndex[a]=b}),window.performance.measure||(window.performance.measure=window.performance.webkitMeasure||function(a,b,c){b=_marksIndex[b].startTime,c=_marksIndex[c].startTime,_marks.push({name:a,entryType:"measure",startTime:b,duration:c-b})}),window.performance.getEntriesByType||(window.performance.getEntriesByType=window.performance.webkitGetEntriesByType||function(a){return _filterEntries("entryType",a)}),window.performance.getEntriesByName||(window.performance.getEntriesByName=window.performance.webkitGetEntriesByName||function(a){return _filterEntries("name",a)}),window.performance.clearMarks||(window.performance.clearMarks=window.performance.webkitClearMarks||function(a){_clearEntries("mark",a)}),window.performance.clearMeasures||(window.performance.clearMeasures=window.performance.webkitClearMeasures||function(a){_clearEntries("measure",a)})}(window),amzncsm.log=function(a){try{-1!==window.location.href.indexOf("csm_debug_mode")&&window.console&&window.console.log(a)}catch(b){b.message&&amzncsm.errors.push(b.message)}},amzncsm.loadModules=function(a,b){var c,d,e;try{for(c=0;c<b.length;c++)if(d=b[c].name,e=b[c].params||[],window.performance&&window.performance.mark&&"function"==typeof window.performance.mark&&window.performance.mark("loadStart"+d+a),"[object Array]"!==Object.prototype.toString.call(e)&&amzncsm.log("Params passed in the amzncsm.loadModules methods must be an array"),amzncsm[d]){if(void 0===amzncsm[d].shortName)throw new amzncsm.exceptions.invalidModuleException("Module shortName not defined for module "+d+". ");e.unshift(a),amzncsm[d].init.apply(amzncsm[d],e),amzncsm.log("Initiated "+d+" module"),window.performance&&window.performance.mark&&"function"==typeof window.performance.mark&&(window.performance.mark("loadEnd"+d+a),window.performance.measure("lt"+amzncsm[d].shortName+a,"loadStart"+d+a,"loadEnd"+d+a))}else amzncsm.log("Undefined module "+d)}catch(f){f.message&&amzncsm.errors.push(f.message)}},amzncsm.define=function(a){var b,c,d,e,f,g,h,i;try{for(b=function(a){return"string"==typeof a?amzncsm[a]:a},c=Array.prototype.slice.call(arguments),d=c[0],e=c.length>2?c[1]:[],f=c[c.length-1],g=[],h=0,i=e.length;i>h;h++)g.push(b(e[h]));amzncsm[d]=f.apply(f,g)}catch(j){j.message&&amzncsm.errors.push(j.message)}},amzncsm.define("utils",[],function(){var a={shortName:"ut"};return a.init=function(a){amzncsm.log("Initializing utils for "+a)},a.getIEVersion=function(){var a,b,c=-1;return"Microsoft Internet Explorer"==navigator.appName?(a=navigator.userAgent,b=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})"),null!=b.exec(a)&&(c=parseFloat(RegExp.$1))):navigator.userAgent.match(/Trident.*rv\:11\./)?c=11:navigator.userAgent.match(/Edge\/\d+\.\d+/)&&(c=12),c},a.isIE=function(){var a=!1||!!document.documentMode,b=!a&&!!window.StyleMedia;return a||b},a.isFirefox=function(){return"undefined"!=typeof InstallTrigger},a.isIntObsSupported=function(){return"undefined"!=typeof IntersectionObserver},a.isSafari=function(){return/Safari/.test(navigator.userAgent)&&/Apple Computer/.test(navigator.vendor)&&!navigator.userAgent.match("CriOS")},a.isSupportedBrowser=function(){return a.isIE()&&amzncsm.config.supportedBrowsers.IE===!0||a.isFirefox()&&amzncsm.config.supportedBrowsers.Firefox===!0||"undefined"!=typeof IntersectionObserver||a.isSafari()&&amzncsm.config.supportedBrowsers.Safari===!0},a.getPixelID=function(){var a=null;return function(){return null===a&&(a=Math.random().toString(36).substr(2)),a}}(),a.detachAllHandlers=function(){var a,b;for(b=0;b<amzncsm.eventHandlers.length;b++)a=amzncsm.eventHandlers[b],amzncsm.removeEvent(a.elem,a.eventName,a.cb)},a.reportClientError=function(a){var b=a===parseInt(a,10)?'{"m": "'+a+'"}':JSON.stringify(amzncsm.constructErrorMessage(a)),c='{"adViewability":[{"error": '+b+'}], "c": "viewability", "api": "'+amzncsm.api+'", "error": 1}';amzncsm.log("ERROR "+b),-1===amzncsm.errorMessages.indexOf(b)&&(amzncsm.errorMessages.push(b),amzncsm.scrollImitationTimer&&clearInterval(amzncsm.scrollImitationTimer),amzncsm.sendPixel(c,""))},a.setTimeout=function(b,c){return setTimeout(function(){try{b.call()}catch(c){c.message&&amzncsm.log("Error: "+c.message),a.reportClientError(c)}},c)},a.setInterval=function(b,c){return setInterval(function(){try{b.call()}catch(c){c.message&&amzncsm.log("Error: "+c.message),a.reportClientError(c)}},c)},a.injectJSResource=function(a,b,c,d,e){function f(){var h=document.createElement("script");c&&(h.onload=c),d&&(h.onerror=function(){g>=e?d(new Error("Unable to load javascript resource : "+b)):(g+=1,f())}),h.src=b,a&&a.body&&null!==a.body&&"function"==typeof a.body.appendChild?a.body.appendChild(h):d&&d(new Error("Unable to append "+b+" to document.body"))}var g=0;f()},a.isLikelyDesktop=function(){if(window&&window.screen){var a=window.screen.width&&window.devicePixelRatio?window.screen.width*window.devicePixelRatio:0;return a>=1024}return window.innerWidth>=1024},a.encr=function(a){if(null!=a&&""!=a&&void 0!=a){var b,c,d="",e="",f=a.length,g=Math.floor(f/2);for(b=0,c=g;g>b&&f>c;b++,c++)d+=a.charAt(b)+a.charAt(c);for(f%2==1&&(d+=a.charAt(a.length-1)),b=0;f>b;b++)e+=String.fromCharCode(d.charCodeAt(b)+d.charCodeAt(b)%5);return encodeURIComponent(e)}return a},a}),amzncsm.define("eventUtils",[],function(){var a={shortName:"eu"};return a.init=function(b){amzncsm.log("Initializing eventUtils for "+b),a.eventHandlers=[]},a.addEvent=function(b,c,d,e){b.addEventListener?b.addEventListener(c,d,e):b.attachEvent&&b.attachEvent("on"+c,d);var f={elem:b,eventName:c,cb:d};a.eventHandlers.push(f)},a.addCustomEvent=function(a,b,c){document.addEventListener?document.addEventListener(a,b,c):document.attachEvent?document.attachEvent("on"+a,function(){return b.call(document,window.event)}):document["on"+a]=b},a.removeEvent=function(a,b,c){document.removeEventListener?a.removeEventListener(b,c,!1):document.detachEvent&&a.detachEvent("on"+b,c)},a.createEvent=function(){return document.createEvent?function(a){var b=document.createEvent("Event");return b.initEvent(a,!0,!0),b}:function(a){var b=document.createEventObject();return b.eventType=a,b}}(),a.dispatchCustomEvent=function(b){document.dispatchEvent?document.dispatchEvent(a.createEvent(b)):document[b]?document[b]():document["on"+b]&&document["on"+b]()},a.stopPropagation=function(a){return a.stopPropagation?void a.stopPropagation():void(a.cancelBubble=!0)},a.preventDefault=function(a){a.preventDefault&&a.preventDefault(),a.returnValue=!1},a.getClickXY=function(a){return"pageX"in a?{pageX:a.pageX,pageY:a.pageY}:{pageX:a.clientX+document.documentElement.scrollLeft,pageY:a.clientY+document.documentElement.scrollTop}},a.handleVisibilityChange=function(b,c){var d,e=b.document;"undefined"!=typeof e.hidden?d="visibilitychange":"undefined"!=typeof e.mozHidden?d="mozvisibilitychange":"undefined"!=typeof e.msHidden?d="msvisibilitychange":"undefined"!=typeof e.webkitHidden&&(d="webkitvisibilitychange"),d&&a.addEvent(e,d,c,!1)},a.isPageVisibilitySupported=function(){return"undefined"!=typeof document.hidden||"undefined"!=typeof document.mozHidden||"undefined"!=typeof document.msHidden||"undefined"!=typeof document.webkitHidden},a.registerPostMessageHandler=function(a){var b=window.addEventListener?"addEventListener":"attachEvent",c=window[b],d="attachEvent"==b?"onmessage":"message";c(d,function(b){var c=b.message?"message":"data",d=b[c];a(d)},!1)},a.getVisibilityState=function(a){return"undefined"==typeof a.visibilityState?"Unknown":a.visibilityState},a.documentHasFocus=function(a){var b=!0;try{b=a.hasFocus?a.hasFocus():!0}catch(c){amzncsm.reportClientError(c)}return b},a.isWindowActive=function(b){var c,d;return b=b||window.document,"undefined"!=typeof b.hidden?c="hidden":"undefined"!=typeof b.mozHidden?c="mozHidden":"undefined"!=typeof b.msHidden?c="msHidden":"undefined"!=typeof b.webkitHidden&&(c="webkitHidden"),d=a.documentHasFocus(b),"undefined"==typeof b[c]?d:!b[c]},a}),amzncsm.define("pixelQueue",[],function(){var a,b,c={shortName:"pq"},d=2500,e=4;return c.init=function(a){amzncsm.csmReport.pq={queue:[]}},c.firePixel=function(a,c){var d,e,f,g;if(b(a)!==!1){try{d=JSON.parse(a),d.ver=amzncsm.version,a=JSON.stringify(d),window.amzncsm.bnmk===!0&&(e={pixelId:d.pixelId,iframeId:document.body.getAttribute("data-absoluteparentid")},f={bnmk:e},g="bnmk/",amzncsm.sendPixel(JSON.stringify(f),g),window.amzncsm.bnmk=!1)}catch(h){}amzncsm.sendPixel(a,c)}},a=function(){var a=amzncsm.csmReport.pq;a.queue.length>0&&(a.queue=[])},b=function(a){var b="";if(("string"==typeof a||a instanceof String)==!1){b="Expected string: but "+typeof a+"found. ","object"==typeof a&&(b+=JSON.stringify(a));try{throw new amzncsm.exceptions.invalidStringException(b)}catch(c){amzncsm.reportClientError(c)}return!1}return!0},c.addPixel=function(c){var f=this,g=amzncsm.csmReport.pq;b(c)!==!1&&(amzncsm.log("Received pixel "+c),0===g.queue.length&&amzncsm.setTimeout(function(){g.queue.length>0&&(f.send(),a())},d),g.queue.push(JSON.parse(c)),g.queue.length>=e&&(f.send(),a()))},c.send=function(){var a=amzncsm.getPixelID(),b=(new Date).getTime(),d=amzncsm.csmReport.pq;d.queue.length>0&&c.firePixel('{"adCsm": '+JSON.stringify(d.queue)+', "pixelId": "'+a+'", "ts": '+b+"}","")},c}),amzncsm.define("viewDuration",["pixelQueue"],function(){var a,b={shortName:"vd"};return b.init=function(b){var c,d;amzncsm.csmReport[b].vd={viewStartTime:0,maxContDuration:0,totalDuration:0,isVisible:!1,pixelReportTime:[1e3,2e3,4e3,8e3,16e3],pixelTimerHandler:null,pCount:0},c=amzncsm.csmReport[b].vd,c.visHandler=function(d){amzncsm.log("Current State: Visible. Time: "+performance.now()),c.isVisible===!1&&(c.viewStartTime=performance.now(),c.pixelTimerHandler=amzncsm.setInterval(function(){a(b)},1e3)),c.isVisible=!0},amzncsm.addCustomEvent("amzncsmVis",c.visHandler,!1),c.invHandler=function(a){amzncsm.log("Current State: Invisible. Time: "+performance.now()),c.isVisible===!0&&(c.totalDuration+=performance.now()-c.viewStartTime,window.clearInterval(c.pixelTimerHandler)),c.isVisible=!1},amzncsm.addCustomEvent("amzncsmInv",c.invHandler,!1),"sendBeacon"in navigator&&(d="onpagehide"in window?"pagehide":"unload",amzncsm.addEvent(window,d,function(){var a,b,d;c.totalDuration>0&&(a=c.maxContDuration,c.isVisible===!0&&(b=performance.now()-c.viewStartTime,b>a&&(a=b)),d={adCsm:[{vdr:a.toFixed(2),tdr:c.totalDuration.toFixed(2)}],pixelId:amzncsm.getPixelID(),ts:(new Date).getTime(),ver:amzncsm.version},amzncsm.sendPixel(JSON.stringify(d),"",!0))},!1))},a=function(a){var b,c,d=amzncsm.csmReport[a].vd;if(d.isVisible===!0){for(b=performance.now()-d.viewStartTime;d.pCount<d.pixelReportTime.length&&b>=d.pixelReportTime[d.pCount];)d.totalDuration+=b,d.maxContDuration=b,amzncsm.log("Reporting pixel "+b.toFixed(2)+" Time: "+performance.now()),c={vdr:b.toFixed(2),tdr:d.totalDuration.toFixed(2)},amzncsm.pixelQueue.addPixel(JSON.stringify(c)),d.pCount++;d.pCount===d.pixelReportTime.length&&window.clearInterval(d.pixelTimerHandler)}},b}),!function(a,b){var c,d;if("object"==typeof exports&&"object"==typeof module)module.exports=b();else if("function"==typeof define&&define.amd)define([],b);else{c=b();for(d in c)("object"==typeof exports?exports:a)[d]=c[d]}}(window,function(){function a(d){if(c[d])return c[d].exports;var e=c[d]={i:d,l:!1,exports:{}};return b[d].call(e.exports,e,e.exports,a),e.l=!0,e.exports}return c={},a.m=b=[function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0});var d={};b.init=function(a){d=a},b.addPixel=function(a){-1!==window.location.href.indexOf("tpm_debug_mode")&&TEST_IDS.push(a);var b=d.firePixel;return(void 0===b?function(){}:b).call(d,JSON.stringify(a),""),!0}},function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.VENDOR_NAME="name",b.VENDOR_URL="url",b.VENDOR_URL_TYPE="type",b.VENDOR_URLS="urls",b.AD_ID="adId",b.UNDEFINED_TYPE="undefined",b.JS="js",b.TPM_DEBUG_MODE="tpm_debug_mode";var d=b.TPM_VENDOR_SCRIPT="tpmVendorScript",e=b.TPM_HTML="tpmHtml",f=b.START="Start";b.END="End",b.TOT="Tot",b.TPM_HTML_START=e+f,b.TPM_HTML_END=e+"End",b.TPM_HTML_TOT=e+"Tot",b.TPM_VENDOR_SCRIPT_START=d+f,b.TPM_VENDOR_SCRIPT_END=d+"End",b.TPM_VENDOR_SCRIPT_TOT=d+"Tot",b.SANDBOXED_IFRAME_VENDORS=["FRAUDSENSOR"]},function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.createSandboxedIFrame=function(a){var b=window.document.createElement("IFRAME");return b.id=a,b.height="0px !important",b.width="0px !important",b.style.visibility="hidden",b.style.opacity=0,b.style.top="-10000px",b.style.left="-10000px",b.sandbox="allow-scripts allow-same-origin allow-forms",b},b.createScript=function(a){var b=window.document.createElement("script");return b&&(b.type="text/javascript",b.src=a),b},b.checkWindowPerformanceExists=function(){return window.performance&&window.performance.mark&&"function"==typeof window.performance.mark}},function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.reportMalformedVendorCtxError=function(a){var b,c,f,g,h=!0,i=!1,j=void 0;try{for(c=Object.keys(a)[Symbol.iterator]();!(h=(b=c.next()).done);h=!0)f=b.value,f.toLowerCase().includes(e.VENDOR_URL)&&delete a[f]}catch(a){i=!0,j=a}finally{try{!h&&c["return"]&&c["return"]()}finally{if(i)throw j}}g={thirdPartyMeasurement:{error:{msg:"Malformed vendor context or vendor config not found",errorType:"MALFORMED_VENDOR_CONTEXT",vendorCtx:JSON.stringify(a)}}},d.addPixel(g)},b.reportResourceLoadingError=function(a,b,c){var f=new URL(c),g={thirdPartyMeasurement:{error:{msg:"Error loading vendorTag",errorType:"RESOURCE_LOADING_ERROR",vendorName:a[e.VENDOR_NAME],vendorUrlType:b,vendorEndPoint:f.hostname}}};d.addPixel(g)},b.reportIFrameError=function(a,b,c){var f={thirdPartyMeasurement:{error:{msg:"Error in IFrame "+a.name+": "+a.message,errorType:"IFRAME_ERROR",vendorName:b[e.VENDOR_NAME],vendorUrlType:c}}};d.addPixel(f)};var d=c(0),e=c(1)},function(a,b,c){"use strict";function d(a){var b,c;if(a&&a.__esModule)return a;if(b={},null!=a)for(c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b[c]=a[c]);return b["default"]=a,b}Object.defineProperty(b,"__esModule",{value:!0}),b.TPM=void 0;var e=d(c(0)),f=d(c(5)),g=d(c(7)),h=d(c(3)),i=d(c(2)),j=c(8),k=c(1);b.TPM={init:function(a){var b,c,d,l=0<arguments.length&&void 0!==a?a:{},m=l.pixelQueue,n=void 0===m?{}:m,o=l.vendorCtxArr,p=void 0===o?[]:o,q=l.adId,r=void 0===q?"":q;try{e.init(n),b=-1!==window.location.href.indexOf(k.TPM_DEBUG_MODE),c=j.PROD_CDN_ENDPOINT,b&&(c=j.TEST_CDN_ENDPOINT),d=i.createSandboxedIFrame("tpmHTMLIFrame"+r),d.onload=function(){var a;i.checkWindowPerformanceExists()&&(window.performance.mark(k.TPM_HTML_END+r),window.performance.measure(k.TPM_HTML_TOT+r,k.TPM_HTML_START+r,k.TPM_HTML_END+r),a={thirdPartyMeasurement:{latency:{msg:"LS_TPM_HTML",tpmHtmlStart:window.performance.getEntriesByName(k.TPM_HTML_START+r)[0].startTime,tpmHtmlEnd:window.performance.getEntriesByName(k.TPM_HTML_END+r)[0].startTime,tpmHtmlTot:window.performance.getEntriesByName(k.TPM_HTML_TOT+r)[0].duration}}},e.addPixel(a)),p.forEach(function(a){f.checkVendorCtxSanity(a)?a[k.VENDOR_URLS].forEach(function(b){var e,g=b[k.VENDOR_URL_TYPE];f.checkVendorUrlSanity(b)&&g.includes(k.JS)&&(i.checkWindowPerformanceExists()&&window.performance.mark(k.TPM_VENDOR_SCRIPT_START+a[k.VENDOR_NAME]+g+r),e={vendorTag:f.getVendorTag(a,b),vendorCtx:a,vendorUrlType:g,adId:r},k.SANDBOXED_IFRAME_VENDORS.includes(a[k.VENDOR_NAME])?d.contentWindow.postMessage(e,c):f.appendVendorScript(e))}):h.reportMalformedVendorCtxError(a)})},i.checkWindowPerformanceExists()&&window.performance.mark(k.TPM_HTML_START+r),d.src=c+"/bao-csm/tpm/third_party_measurement.html",window.addEventListener("message",function(a){if(a.origin===c&&a.source===d.contentWindow&&a.data.adId===r)switch(a.data.msgType){case"vendorTagLoaded":f.reportVendorLatency(a.data.vendorCtx,a.data.vendorUrlType,r);break;case"vendorScriptLoadError":h.reportResourceLoadingError(a.data.vendorCtx,a.data.vendorUrlType,a.data.vendorTag);break;case"vendorScriptError":h.reportIFrameError(a.data.error,a.data.vendorCtx,a.data.vendorUrlType)}}),0<p.length&&window.document.body.appendChild(d)}catch(a){g.reportError(a)}}}},function(a,b,c){"use strict";function d(a){var b,c;if(a&&a.__esModule)return a;if(b={},null!=a)for(c in a)Object.prototype.hasOwnProperty.call(a,c)&&(b[c]=a[c]);return b["default"]=a,b}var e,f,g,h,i,j,k;Object.defineProperty(b,"__esModule",{value:!0}),b.appendVendorScript=b.checkVendorUrlSanity=b.checkVendorCtxSanity=b.reportVendorLatency=b.getVendorTag=void 0,e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},f=d(c(0)),g=c(1),h=c(6),i=d(c(2)),j=d(c(3)),b.getVendorTag=function(a,b){var c,d,e,f=new URL(b[g.VENDOR_URL]),h=!0,i=!1,j=void 0;try{for(d=Object.keys(a)[Symbol.iterator]();!(h=(c=d.next()).done);h=!0)e=c.value,[g.VENDOR_URLS,g.VENDOR_NAME].includes(e)||f.searchParams.append(e,a[e])}catch(a){i=!0,j=a}finally{try{!h&&d["return"]&&d["return"]()}finally{if(i)throw j}}return f.toString()},k=b.reportVendorLatency=function(a,b,c){var d,e=a[g.VENDOR_NAME];i.checkWindowPerformanceExists()&&(window.performance.mark(g.TPM_VENDOR_SCRIPT_END+e+b+c),window.performance.measure(g.TPM_VENDOR_SCRIPT_TOT+e+b+c,g.TPM_VENDOR_SCRIPT_START+e+b+c,g.TPM_VENDOR_SCRIPT_END+e+b+c)),d={thirdPartyMeasurement:{latency:{msg:"LS_"+e+"_"+b,vendorName:e,vendorUrlType:b,tpmStart:window.performance.getEntriesByName(g.TPM_VENDOR_SCRIPT_START+e+b+c)[0].startTime,tpmEnd:window.performance.getEntriesByName(g.TPM_VENDOR_SCRIPT_END+e+b+c)[0].startTime,tpmTot:window.performance.getEntriesByName(g.TPM_VENDOR_SCRIPT_TOT+e+b+c)[0].duration}}},f.addPixel(d)},b.checkVendorCtxSanity=function(a){return!h.isEmpty(a)&&e(a[g.VENDOR_NAME])!==g.UNDEFINED_TYPE&&e(a[g.VENDOR_URLS])!==g.UNDEFINED_TYPE},b.checkVendorUrlSanity=function(a){return!h.isEmpty(a)&&e(a[g.VENDOR_URL])!==g.UNDEFINED_TYPE&&e(a[g.VENDOR_URL_TYPE])!==g.UNDEFINED_TYPE},b.appendVendorScript=function(a){var b=i.createScript(a.vendorTag);b&&(b.onload=function(){k(a.vendorCtx,a.vendorUrlType,a.adId)},b.onerror=function(){j.reportResourceLoadingError(a.vendorCtx,a.vendorUrlType,a.vendorTag)},window.document.body.appendChild(b))}},function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.isEmpty=void 0;var d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},e=c(1);b.isEmpty=function(a){return(void 0===a?"undefined":d(a))===e.UNDEFINED_TYPE||null===a||""===a}},function(a,b,c){"use strict";function d(a,b){try{return a[b]}catch(a){return}}Object.defineProperty(b,"__esModule",{value:!0}),b.reportError=function(a){var b=window.location.host+window.location.pathname,c={thirdPartyMeasurement:{error:{msg:a===parseInt(a,10)?{m:a,debug:encodeURIComponent(b.replace(/\//g,"_"))}:function(a,b){if(b=b||{},!a)return{};a.m&&a.m.message&&(a=a.m);var c,e,j,k,l,m={m:function(a,b){var c=b.m||b.message||"";return c+=a.m&&a.m.message?a.m.message:a.m&&a.m.target&&a.m.target.tagName?"Error handler invoked by "+a.m.target.tagName+" tag":a.m?a.m:a.message?a.message:"Unknown error"}(a,b),c:a.c?""+a.c:a.c,s:[],l:a.l||a.line||a.lineno||a.lineNumber,name:a.name,type:a.type},n=0,o=0;if((c=a.stack||(a.err?a.err.stack:""))&&c.split)for(e=c.split("\n");n<e.length&&m.s.length<f;)(j=e[n++])&&m.s.unshift(function(a){return a?a.replace(/^\s+|\s+$/g,""):void 0}(j));else for(k=d(a.args||arguments,"callee"),o=n=0;k&&f>n;)l=g,k.skipTrace||(j=k.toString())&&j.substr&&(l=0===o?4*g:l,l=1==o?2*g:l,m.s.unshift(j.substr(0,l)),o++),k=d(k,"caller"),n++;return!m.f&&0<m.s.length&&function(a){if(a&&a.s){var b,c=0<a.s.length?a.s[0]:"",d=1<a.s.length?a.s[1]:"";c&&(b=c.match(i)),b&&3==b.length||!d||(b=d.match(h)),b&&3==b.length&&(a.f=b[1],a.l=b[2])}}(m),m}(a),errorType:"TPM_ERROR"}}};e.addPixel(c)};var e=c(0),f=20,g=256,h=/\(?([^\s]*):(\d+):\d+\)?/,i=/.*@(.*):(\d*)/},function(a,b,c){"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.PROD_CDN_ENDPOINT="https://c.amazon-adsystem.com",b.TEST_CDN_ENDPOINT="https://d2mchadidjklqn.cloudfront.net"}],a.c=c,a.d=function(b,c,d){a.o(b,c)||Object.defineProperty(b,c,{enumerable:!0,get:d})},a.r=function(a){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(a,"__esModule",{value:!0})},a.t=function(b,c){var d,e;if(1&c&&(b=a(b)),8&c)return b;if(4&c&&"object"==typeof b&&b&&b.__esModule)return b;if(d=Object.create(null),a.r(d),Object.defineProperty(d,"default",{enumerable:!0,value:b}),2&c&&"string"!=typeof b)for(e in b)a.d(d,e,function(a){return b[a]}.bind(null,e));return d},a.n=function(b){var c=b&&b.__esModule?function(){return b["default"]}:function(){return b};return a.d(c,"a",c),c},a.o=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},a.p="",a(a.s=4);var b,c}),amzncsm.detectViewability=function(a,b,c,d){function e(){return r.beaconSent.iab&&r.beaconSent.groupm&&r.beaconSent.amazon&&r.beaconSent.vdr&&r.beaconSent.mrc}function f(a,b){if(r.getDuration){var c=r.end[b]-r.start[b];p(a+" viewed("+b+") for: "+c),r.totalTime+=c}}function g(a){p(a+" IN VIEW")}function h(a,b){p(a+" NOT IN VIEW for "+b+" standard")}function i(a){var b,c,d,e,f;if(r.beaconSent[a]!==!0)try{b=a===v?0:.05,c=r.detectActualDimension(r.adElementContainer),d=amzncsm.isWindowActive(r.hostDoc),e=c>=r.areaCutoff[a]-b&&d,f=((new Date).getTime()/1e3-r.start[a]).toFixed(3),e&&(m("viewable",{viewable:!0,defn:a,p:(100*r.timerStartArea[a]).toFixed(0),t:f}),p(r.adElementContainer+" displayed for "+r.timeCutoff[a]/1e3+" seconds"),r.beaconSent[a]=!0,"iab"===a&&amzncsm.dispatchCustomEvent("amzncsmIabView"),j())}catch(g){p(g),amzncsm.reportClientError(g)}}function j(){e()&&amzncsm.scrollImitationTimer&&clearInterval(amzncsm.scrollImitationTimer)}function k(a){q(a>r.areaCutoff.iab&&amzncsm.isWindowActive(r.hostDoc)?!0:!1)}function l(a,b,c,d,e){var f=0,g=!1,h=!0;r.visibilityState=amzncsm.getVisibilityState(r.hostDoc),amzncsm.isIntObsSupported()?g=!0:amzncsm.isIE()?f=r.fractionInViewportIE():amzncsm.isFirefox()?f=r.fractionInViewportFF(a,b):amzncsm.isSafari()?(r.fractionInViewportSafari(c,d),g=!0):h=!1,0>f&&(h=!1),h===!1&&c===!0&&m("unmeasured",{defn:"unmeasured",p:0,t:0}),e||(g=!1,f=r.area),g===!1&&h===!0&&d(f)}function m(a,b){var c,d=(new Date).getTime(),e="DTB"===amzncsm.api&&amzncsm.outsideImpIframe!==!0||"ONO"===amzncsm.api||amzncsm.isBen===!0?"_ben":"",f=navigator&&void 0!==navigator.buildID?navigator.buildID:"undefined";b.viewable===!0&&window.performance&&window.performance.timing&&(c=((d-(window.performance.timing.responseStart||window.performance.timing.domLoading))/1e3).toFixed(2));try{switch(a){case"above_the_fold":o('{"atf'+e+'": '+b.atf+", "+(null!==r.area&&isNaN(r.area)===!1?'"f": '+r.area.toFixed(2)+", ":"")+'"vs": "'+(void 0!==r.visibilityState?r.visibilityState:"undefined")+'", '+(null!==r.leftPos&&isNaN(r.leftPos)===!1?'"left": '+r.leftPos.toFixed(0)+", ":"")+(null!==r.topPos&&isNaN(r.topPos)===!1?'"top": '+r.topPos.toFixed(0)+", ":"")+'"ah": '+(void 0!==r.adHeight?r.adHeight:'"undefined"')+', "aw": '+(void 0!==r.adWidth?r.adWidth:'"undefined"')+', "ts": '+d+', "bn": '+(amzncsm.outsideImpIframe===!0?!0:!1)+", "+(amzncsm.isFirefox()===!0?'"buildId": "'+f+'", ':"")+'"pixelId": "'+r.pixelId+'"}',""===e?"atf/":"");break;case"viewable":o('{"v'+e+'": {"p": '+b.p+', "t": '+b.t+', "def": "'+b.defn+'"}, "vs": "'+(void 0!==r.visibilityState?r.visibilityState:"undefined")+'", '+(null!==r.leftPos&&isNaN(r.leftPos)===!1?'"left": '+r.leftPos.toFixed(0)+", ":"")+(null!==r.topPos&&isNaN(r.topPos)===!1?'"top": '+r.topPos.toFixed(0)+", ":"")+'"ah": '+(void 0!==r.adHeight?r.adHeight:'"undefined"')+', "aw": '+(void 0!==r.adWidth?r.adWidth:'"undefined"')+', "ttv": '+(void 0!==c?c:'"undefined"')+', "ts": '+d+', "bn": '+(amzncsm.outsideImpIframe===!0?!0:!1)+', "pixelId": "'+r.pixelId+'"}',""===e?"v/":"");
break;case"unmeasured":o('{"atf'+e+'": "unknown", "vs": "'+(void 0!==r.visibilityState?r.visibilityState:"undefined")+'", "ts": '+d+', "bn": '+(amzncsm.outsideImpIframe===!0?!0:!1)+', "pixelId": "'+r.pixelId+'"}',""===e?"atf/":"")}}catch(g){p(g),amzncsm.reportClientError(g)}}function n(a,b){function c(a){r.beaconSent[a]===!1&&(r.setTimeoutHandler[a]&&(p("Cleared setTimeout call"),clearTimeout(r.setTimeoutHandler[a])),r.setTimeoutHandler[a]=amzncsm.setTimeout(function(){p("Setting function on setTimeout"),i(a)},r.timeCutoff[a]))}function d(a){g(B),r.start[a]=(new Date).getTime()/1e3,c(a),r.timerStarted[a]=!0}function e(a){clearTimeout(r.setTimeoutHandler[a]),r.timerStarted[a]=!1,r.end[a]=(new Date).getTime()/1e3,h(B,a),f(B,a)}function n(a){q(a),s(a),w(a),x(a)}function o(a,b){amzncsm.isWindowActive(r.hostDoc)&&(r.focused===!1||null===r.focused)&&b>r.areaCutoff[a]&&r.timerStarted[a]===!1&&(r.timerStartArea[a]=b,d(a)),amzncsm.isWindowActive(r.hostDoc)||(p("Element out of focus. Clearing the "+a+" timer"),e(a)),b>=r.areaCutoff[a]&&r.timerStarted[a]===!1?(r.timerStartArea[a]=b,d(a)):b<r.areaCutoff[a]&&r.timerStarted[a]===!0&&(p("Element is not enough visible. Clearing the "+a+" timer"),e(a))}function q(a){r.beaconSent.mrc===!1&&amzncsm.isBTRCompliant===!0&&o(v,a)}function s(a){r.beaconSent.iab===!1&&o(t,a)}function w(a){r.beaconSent.groupm===!1&&o(u,a)}function x(a){r.beaconSent.amazon===!1&&a>=r.areaCutoff.amazon&&(m("viewable",{viewable:!0,defn:"amzn",p:a,t:0}),r.beaconSent.amazon=!0,j())}var y,z,A=r.adElement,B=r.adElementContainer,C=r.adID;amzncsm.resetTimer=e,y=function(a){var b=amzncsm.isWindowActive(r.hostDoc),c=!1;r.area=a,p("Area fraction inside viewport "+r.area+" hasFocus "+b),r.area>r.areaCutoff.iab&&(c=!0),n(r.area),window.performance&&window.performance.mark&&"function"==typeof window.performance.mark&&(window.performance.mark("atfCompEnd"+C),window.performance.measure("atfComp"+C,"atfCompStart"+C,"atfCompEnd"+C)),r.beaconSent.atf===!1&&(m("above_the_fold",{atf:c}),r.beaconSent.atf=!0),k(a)},z=function(a){n(a),amzncsm.isWindowActive(r.hostDoc)?r.focused=!0:r.focused=!1,k(a),r.area=a},a===!0?(amzncsm.isIntObsSupported()?r.initViewCompIntObs(y,z):amzncsm.isSafari()&&r.initVCompSafari(),window.performance&&window.performance.mark&&"function"==typeof window.performance.mark&&window.performance.mark("atfCompStart"+C),l(A,B,a,y,b)):l(A,B,a,z,b)}function o(){var a=Array.prototype.slice.call(arguments);amzncsm.pixelQueue.firePixel.apply(amzncsm.beacon,a)}function p(a){try{r.debug&&window.console&&window.console.log(a)}catch(b){}}function q(a){r.beaconSent.vdr!==!0&&(a===!0?amzncsm.dispatchCustomEvent("amzncsmVis"):amzncsm.dispatchCustomEvent("amzncsmInv"))}var r=this,s=amzncsm.isSafari()?500:1e3,t="iab",u="groupm",v="mrc";return this.adElement=a,this.adElementContainer=a,this.adWidth=a.document?a.document.body.offsetWidth:a.offsetWidth,this.adHeight=a.document?a.document.body.offsetHeight:a.offsetHeight,this.adID=d,this.getDuration=!0,this.totalTime=0,this.leftPos=null,this.topPos=null,this.wrapCreativeArea=242500,this.start={iab:0,groupm:0,mrc:0},this.end={iab:0,groupm:0,mrc:0},this.area=0,this.timerStartArea={iab:0,groupm:0,mrc:0},this.areaCutoff={iab:.5,groupm:.9,amazon:.05,mrc:.5},this.reportingArea={iab:50,groupm:100,amazon:0,mrc:50},this.timeCutoff={iab:1e3,groupm:1e3,mrc:1e3},this.focused=null,this.setTimeoutHandler={iab:null,groupm:null,mrc:null},this.timerStarted={iab:!1,groupm:!1,mrc:!1},this.beaconSent={atf:!1,iab:!1,groupm:!1,amazon:!1,mrc:!1,vdr:!1},this.isBTRCompliantInterval=null,this.hostWindow=b,this.hostDoc=c,this.pixelId=amzncsm.getPixelID(),amzncsm.visibilityIO=!1,amzncsm.IOV2Supported=!1,this.adElement&&this.adElement.document&&"IFRAME"!==this.adElement.tagName&&(this.adElement=this.adElement.document.body),this.adElementContainer&&this.adElementContainer.document&&"IFRAME"!==this.adElementContainer.tagName&&(this.adElementContainer=this.adElementContainer.document.body),amzncsm.isLikelyDesktop()&&r.adHeight*r.adWidth>=r.wrapCreativeArea&&(r.areaCutoff.iab=.3,r.reportingArea.iab=30,r.areaCutoff.mrc=.3,r.reportingArea.mrc=30),amzncsm.isSupportedBrowser()===!1?(m("unmeasured",{defn:"unmeasured",p:0,t:0}),!1):(amzncsm.csmReport[this.adID].container=this.adElementContainer,amzncsm.csmReport[this.adID].adElement=this.adElement,this.debug=-1!==window.location.href.indexOf("csm_debug_mode"),this.profile=-1!==window.location.href.indexOf("amzn_profile_mode"),amzncsm.log=p,void(this.collectData=function(){var a,b=this;n(!0,!0),a=function(a){try{if(document.visibilityState&&amzncsm.log("Visibility State "+document.visibilityState),e()===!1&&amzncsm.isWindowActive(b.hostDoc))var c=amzncsm.setTimeout(function(){c&&clearTimeout(c);var b=!(a&&(a.type.includes("visibilitychange")||"btrCompliant"===a.type));n(!1,b)},50);else q(!1),amzncsm.resetTimer(t),amzncsm.resetTimer(u),amzncsm.resetTimer(v)}catch(d){p(d),amzncsm.reportClientError(d)}},amzncsm.scrollImitationTimer=amzncsm.setInterval(function(){if(amzncsm.isIntObsSupported());else if(amzncsm.isFirefox())(!b.oldMozInnerScreenX||!b.oldMozInnerScreenY||Math.abs(window.mozInnerScreenX-b.oldMozInnerScreenX)>10||Math.abs(window.mozInnerScreenY-b.oldMozInnerScreenY)>10)&&(amzncsm.log("Triggered deep check"),a()),b.oldMozInnerScreenX=window.mozInnerScreenX,b.oldMozInnerScreenY=window.mozInnerScreenY;else if(amzncsm.isIE()){var c=document.elementFromPoint(b.adWidth/2,b.adHeight/2);null!==c&&amzncsm.isWindowActive(b.hostDoc)?(amzncsm.log("Triggered deep check"),a()):q(!1)}else amzncsm.isSafari()&&a()},s),amzncsm.handleVisibilityChange(this.hostWindow,a),amzncsm.addEvent(this.hostWindow,"resize",a,!1),this.isBTRCompliantInterval=amzncsm.setInterval(function(){amzncsm.isBTRCompliant===!0&&(clearInterval(b.isBTRCompliantInterval),a({type:"btrCompliant"}))},100),amzncsm.isIntObsSupported()&&amzncsm.addCustomEvent("amzncsmVChng",function(){a()}),amzncsm.addCustomEvent("amzncsmVdrcompl",function(){b.beaconSent.vdr=!0})}))},amzncsm.extend=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a.prototype[c]=b[c])},window.amzncsm.rmR=function(a,b){amzncsm.api="RTB",amzncsm.vendorCtxArr=null==b?[]:b,startCsmComp(a)},startCsmComp=function(a){var b,c,d,e;try{b=function(a){a.message&&(amzncsm.errors.push(a.message),amzncsm.reportErrors())},c=a,d=c.document,e=function(a){var b=a.readyState;return"complete"===b},e(d)?amzncsm.rm({e:c,w:c,d:d}):void 0===document.onreadystatechange?amzncsm.rm({e:c,w:c,d:d}):d.addEventListener("readystatechange",function(){try{e(d)&&amzncsm.rm({e:c,w:c,d:d})}catch(a){b(a)}})}catch(f){b(f)}},window.amzncsm.rmD=function(a,b,c,d,e){amzncsm.api="DTB",amzncsm.outsideImpIframe=!0;var f="https://",g=window.amzncsm.host||"aax.amazon-adsystem.com",h="/x/px/",i=f+g+h+b+"/";startCsmComp(i,c)},window.amzncsm.rmDB=function(a,b){amzncsm.api="DTB",amzncsm.outsideImpIframe=!1,startCsmComp(a,b)},window.amzncsm.rmOB=function(a,b){amzncsm.api="ONO",startCsmComp(a,b)},amzncsm.init=function(){var a,b,c,d,e,f="undefined"!=typeof window&&window&&window.document||document;if(f){if(a=new URL(f.currentScript.src),b=new URLSearchParams(a.search),amzncsm.pixelParams=Object.fromEntries(b.entries()),amzncsm.instrURL=amzncsm.sanitizePixelUrl(b.get("instrUrl"),!0),amzncsm.pixelParams.instrURL=amzncsm.instrURL,amzncsm.isBen=b.get("isBen")||!1,amzncsm.sanitizePixelMap(b.get("pm")),!amzncsm.instrURL||!b.get("pm"))throw amzncsm.errorCodes.MALFORMED_URL;c=b.get("targetElement"),("window"===c||c.includes("document.getElementById"))&&(c=Function('"use strict";return ('+c+")")()),d=b.get("vendorCtxArr"),e=[];try{e=JSON.parse(d)}catch(g){}amzncsm.log("Query params fetched: "+JSON.stringify(amzncsm.pixelParams)+" : "+amzncsm.isBen+" : "+d),window.amzncsm.rmR(c,e)}},amzncsm.init(),amzncsm.extend(amzncsm.detectViewability,{getIframeWindowDimension:function(a,b){var c,d,e,f,g,h=b.body,i=a.innerWidth||b.documentElement.clientWidth||h.clientWidth||640,j=a.innerHeight||b.documentElement.clientHeight||h.clientHeight||480;return window.mozInnerScreenX&&window.mozInnerScreenY&&(c=90,d=a.mozInnerScreenX,e=a.mozInnerScreenY-c-10,f=window.screen.availWidth,g=window.screen.availHeight-c,0!==d&&(0>d?(i+=d,i=Math.max(0,i)):d>f?i=0:f-d>i||(i=f-d),i=Math.min(i,f)),0!==e&&(0>e?(j+=e,j=Math.max(0,j)):g>e?g-e>j||(j=g-e+2):j=0),j=Math.min(j,g)),{width:i,height:j}},getViewPortSize:function(a){var b={width:1/0,height:1/0,area:1/0};return!isNaN(a.document.body.clientWidth)&&a.document.body.clientWidth>0&&(b.width=a.document.body.clientWidth),!isNaN(a.document.body.clientHeight)&&a.document.body.clientHeight>0&&(b.height=a.document.body.clientHeight),a.document.documentElement&&a.document.documentElement.clientWidth&&!isNaN(a.document.documentElement.clientWidth)&&(b.width=a.document.documentElement.clientWidth),a.document.documentElement&&a.document.documentElement.clientHeight&&!isNaN(a.document.documentElement.clientHeight)&&(b.height=a.document.documentElement.clientHeight),a.innerWidth&&!isNaN(a.innerWidth)&&(b.width=Math.min(b.width,a.innerWidth)),a.innerHeight&&!isNaN(a.innerHeight)&&(b.height=Math.min(b.height,a.innerHeight)),b.area=b.height*b.width,b},fractionInViewportIE:function(){var a,b,c,d,e,f=Number.MAX_VALUE,g=Number.MAX_VALUE,h=Number.MIN_VALUE,i=Number.MIN_VALUE,j=10,k=0,l=this.getIframeWindowDimension(window,document),m=l.width,n=l.height,o=(m/j+1)*(n/j+1);for(c=0;m>=c;c+=j)for(d=0;n>=d;d+=j)e=document.elementFromPoint(c,d),null!==e&&(k++,f>c?f=c:c>h&&(h=c),g>d?g=d:d>i&&(i=d));return a=h-f,b=i-g,(0>=a||0>=b)&&(a=0,b=0),amzncsm.log("IE: ad slot visible width = "+a+" height = "+b),amzncsm.log("IE: visiblePoints "+k+" totalPoints "+o+" visibleFraction "+k/o),0!==o?k/o:0},fractionInViewportFF:function(a,b){var c,d,e,f,g,h,i,j,k,l;return!window.mozInnerScreenX&&0!==window.mozInnerScreenX||!window.mozInnerScreenY&&0!==window.mozInnerScreenY?-1:(c=this.getIframeWindowDimension(window,document),d=c.width,e=c.height,f=b.offsetWidth,g=b.offsetHeight,h=100,this.leftPos=Math.abs(window.mozInnerScreenX),this.topPos=Math.abs(window.mozInnerScreenY-h),i=f,j=g,k=this.topPos,l=this.leftPos,l>=0&&(l=a.getBoundingClientRect().left),k>=0&&(k=a.getBoundingClientRect().top),0>k&&(j+=k),k+g>e&&(j-=k+g-e),0>l&&(i+=l),l+f>d&&(i-=l+f-d),0>i&&(i=0),i>f&&(i=f),0>j&&(j=0),j>g&&(j=g),amzncsm.log("Visible Width: "+i+" Visible Height: "+j+" Actual Width: "+f+" Actual Height: "+g+" winWidth "+d+" winHeight "+e),0!==g&&0!==f?i*j/(g*f):0)},initViewCompIntObs:function(a,b){function c(c){c.forEach(function(c){var e,f,g,h;"undefined"!=typeof c.isVisible&&(amzncsm.visibilityIO=c.isVisible,amzncsm.IOV2Supported=!0),e=c.boundingClientRect,f=c.intersectionRect,g=e.width*e.height,null!==g&&isNaN(g)===!1&&g>0&&(h=f.width*f.height/g,d===!1?(d=!0,a.call(null,h)):(b.call(null,h),amzncsm.dispatchCustomEvent("amzncsmVChng")))})}var d=!1,e=new IntersectionObserver(c,{threshold:[.1,.3,.5,.7,.9]});e.observe(document.body),amzncsm.setTimeout(function(){d===!1&&(d=!0,a.call(null,0))},500)},initVCompSafari:function(){function a(a,b){var c,d,e,f=document.createElement("IFRAME"),g='var vs = false; function step() { vs = true; }; function dv() { vs = false; window.requestAnimationFrame(step); window.setTimeout(function() { if (vs === true) { parent.postMessage(window.frameElement.id + " vs", "*"); } else { parent.postMessage(window.frameElement.id + " ivs", "*"); } }, 20); }; (function registerPostMessageHandler() { var eventMethod = window.addEventListener ? "addEventListener" : "attachEvent"; var eventer = window[eventMethod]; var messageEvent = eventMethod == "attachEvent" ? "onmessage" : "message"; eventer(messageEvent, function(e) { var key = e.message ? "message" : "data"; var data = e[key]; if (data == "dv") { dv(); } }, false); })(); ';f.id=b,f.height=5,f.width=5,f.style.position="absolute",f.style.opacity=0;for(c in a)a.hasOwnProperty(c)&&(f.style[c]=a[c]);document.body.appendChild(f);try{d=f.contentDocument,d&&(d.open(),d.writeln("<!DOCTYPE html><html><head><title></title></head><body></body></html>"),d.close(),e=d.createElement("script"),e&&(e.type="text/javascript",e.text=g,d.body.appendChild(e)))}catch(h){amzncsm.reportClientError(h)}return f}amzncsm.safv={itrCount:0,atfPixelFired:!1},a({top:"20px",right:"20px"},"adcfTR"),a({bottom:"20px",left:"20px"},"adcfBL"),a({bottom:"20px",right:"20px"},"adcfBR"),a({top:"20px",left:"20px"},"adcfTL"),a({top:"50%",left:"50%"},"adcfCenter"),a({top:"-1000px",right:"-1000px"},"adcfInv"),amzncsm.setTimeout(function(){amzncsm.safv.atfPixelFired===!1&&amzncsm.safv.atfSuccessCb&&(amzncsm.safv.atfPixelFired=!0,amzncsm.safv.atfSuccessCb.call(null,0))},2e3),amzncsm.registerPostMessageHandler(function(a){var b=a.split(" ");"adcfInv"===b[0]?"vs"===b[1]?(amzncsm.safv.fp=!0,amzncsm.safv.vCount=0):amzncsm.safv.fp=!1:("vs"===b[1]||"ivs"===b[1])&&"vs"===b[1]&&amzncsm.safv.fp===!1&&amzncsm.safv.vCount++,amzncsm.safv.respCount++,6===amzncsm.safv.respCount&&(amzncsm.log("Visible pixel count "+amzncsm.safv.vCount),amzncsm.safv.itrCount++,amzncsm.safv.fp===!1&&amzncsm.safv.itrCount>2&&(amzncsm.safv.atfPixelFired===!0?amzncsm.safv.viewSuccessCb.call(null,amzncsm.safv.vCount/5):amzncsm.safv.atfSuccessCb&&(amzncsm.safv.atfPixelFired=!0,amzncsm.safv.atfSuccessCb.call(null,amzncsm.safv.vCount/5))))})},fractionInViewportSafari:function(a,b){var c,d;for(amzncsm.safv.vCount=0,amzncsm.safv.respCount=0,amzncsm.safv.fp=!1,a?amzncsm.safv.atfSuccessCb=b:amzncsm.safv.viewSuccessCb=b,c=document.getElementsByTagName("iframe"),d=0;d<c.length;d++)/adcf/.test(c[d].id)&&c[d].contentWindow.postMessage("dv","*")},domHitTestingWithBoundary:function(a,b,c,d,e,f,g,h,i){var j,k,l,m,n,o=Number.MAX_VALUE,p=Number.MAX_VALUE,q=Number.MIN_VALUE,r=Number.MIN_VALUE,s=0,t=0;for(k=a;b>=k;k+=e)for(l=c;d>=l;l+=f)s++,j=h.elementFromPoint(k,l),g.call(null,j)&&(t++,i===!0&&(o>k?o=k:k>q&&(q=k),p>l?p=l:l>r&&(r=l)));return m=q-o,n=r-p,(0>=m||0>=n)&&(m=0,n=0),amzncsm.log("domHitTestingWithBoundary: visiblePoints "+t+" totalPoints "+s+" visibleArea "+t/s),i?{height:n,width:m}:0!==s?t/s:0},detectActualDimension:function(a){if(amzncsm.isIntObsSupported()||amzncsm.isSafari())return 1;if(amzncsm.isIE()){var b=a.getBoundingClientRect().top,c=a.getBoundingClientRect().left,d=a.offsetWidth,e=a.offsetHeight,f=function(a){return null!==a};return this.domHitTestingWithBoundary(c,c+d,b,b+e,5,5,f,this.hostDoc,!1)}return 1}}),amzncsm=amzncsm||{},amzncsm.rm=function(){var a,b,c,d,e,f,g,h="r-1.35";return amzncsm.version=h,a="https://ts.amazon-adsystem.com/tg/resources/mrc/crem/crem.js",amzncsm.isBTRCompliant=!1,amzncsm.version=h+"-tpmv1",b={RTB:{viewDuration:!0,hoverStats:!1,pageMetrics:!1,detectBrowser:!1},DTB:{viewDuration:!0,hoverStats:!1,pageMetrics:!1,detectBrowser:!1},ONO:{viewDuration:!0,hoverStats:!1,pageMetrics:!1,detectBrowser:!1},supportedBrowsers:{IE:!0,Firefox:!0,Chrome:!0,Safari:!0,Other:!0}},amzncsm.config=b,c=function(a){var b,c;if(window.performance&&window.performance.getEntriesByType&&"function"==typeof window.performance.getEntriesByType)for(b=window.performance.getEntriesByType("resource"),c=0;c<b.length;++c)if(a.test(b[c].name)===!0)return b[c];return null},d=function(){var a,b,d=new RegExp("csm([\\S]+)?.js"),e=c(d);null!==e&&(a=window.performance.timing&&"undefined"!=typeof window.performance.timing.navigationStart?window.performance.timing.navigationStart:0,b={ns:a,st:e.startTime.toFixed(2),re:e.responseEnd.toFixed(2),ldTot:e.duration.toFixed(2)},amzncsm.pixelQueue.addPixel(JSON.stringify(b)))},e=function(a){var b,c,d,e=window.performance.getEntriesByType("measure"),f={};for(b=0;b<e.length;++b)e[b].name.endsWith(a)&&(c=e[b],d=c.name.substring(0,c.name.length-a.length),f[d]=c.duration.toFixed(2));amzncsm.pixelQueue.addPixel(JSON.stringify(f))},f=function(b){var c,f,h=b.e,i=b.w,j=b.d,k=Math.round(1e7*Math.random()).toString();return amzncsm.csmReport=amzncsm.csmReport||{},amzncsm.csmReport.hasOwnProperty(k)?!1:(amzncsm.csmReport[k]={},amzncsm.log("Initiating CSM computation for ad slot "+k),g(amzncsm.api,h,i,j,k),window.performance&&window.performance.mark&&"function"==typeof window.performance.mark&&window.performance.mark("csmStart"+k),c=function(){window.CREM.evaluateBTRCriteria(document,document.body,function(a){a.isBTRCompliant===!0&&(amzncsm.isBTRCompliant=!0)})},amzncsm.injectJSResource(j,a,c,amzncsm.reportClientError,2),d(),("DTB"===amzncsm.api||"RTB"===amzncsm.api||"ONO"===amzncsm.api)&&(f=new amzncsm.detectViewability(h,i,j,k),f.collectData&&f.collectData()),window.performance&&window.performance.mark&&"function"==typeof window.performance.mark&&(window.performance.mark("csmEnd"+k),window.performance.measure("csmTot"+k,"csmStart"+k,"csmEnd"+k),e(k)),void(amzncsm.errors.length>0&&amzncsm.reportErrors()))},g=function(a,c,d,e,f){var g,h,i,j,k,l,m=amzncsm.getModules(c,d,e);for(g in m)if(m.hasOwnProperty(g)&&(h=m[g],(h["default"]===!0||b[a][g]===!0)&&(amzncsm.loadModules(f,[{name:g,params:h.params}]),h.global===!0)))for(i in amzncsm[g])amzncsm[g].hasOwnProperty(i)&&(amzncsm[i]=amzncsm[g][i]);j=!1,j=!0,j&&"undefined"!=typeof TPM&&TPM.init({pixelQueue:amzncsm.pixelQueue,vendorCtxArr:amzncsm.vendorCtxArr,adId:f}),k=window.amzncsmp||{},k.sia===!0&&(l={instrURL:encodeURI(amzncsm.instrURL),pixelId:amzncsm.getPixelID()},window.top.postMessage(JSON.stringify(l),"*"))},f}()}();