{"/15184186/time.is_homepage_300x250":["html",1,null,null,1,1,1,0,0,null,null,null,1,null,[138215969620],[4479213309],[4468582735],[2178672843],null,null,null,null,null,null,null,1,null,null,null,null,null,null,"AOrYGsk8stAjRAOgIkccVyyOcpZBR0aaHf5wJkxQ1Wtj37Df4xpGF7_pcjR5XsXmjLusULdGh1gkRtouzQRKGQfCY6jY4IVCCW0p47F08_77PaijXa0","CP-_toe6_o4DFQ88RAgdQD0jwQ",null,null,null,null,null,null,null,null,null,null,null,null,null,null,"4",null,null,null,null,null,null,null,null,null,null,null,null,null,null,1]}
<!doctype html><html><head><script>var inDapIF=true,inGptIF=true;</script></head><body leftMargin="0" topMargin="0" marginwidth="0" marginheight="0"><script>window.dicnf = {};</script><script data-jc="42" data-jc-version="r20250807" data-jc-flags="[&quot;x%278446&#39;9efotm(&amp;20067;&gt;8&amp;&gt;`dopb/%&lt;1732261!=|vqc)!7201061?&#39;9efotm(&amp;20723;&gt;:&amp;&gt;`dopb/%&lt;1245;05!=nehu`/!361&lt;&lt;101!9abk{a($16463496&amp;&lt;qqvb/%&lt;1374236!=8(&amp;2042627:&amp;&gt;6x&quot;]">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var r=this||self;function aa(a,b){a:{var c=["CLOSURE_FLAGS"];for(var d=r,f=0;f<c.length;f++)if(d=d[c[f]],d==null){c=null;break a}c=d}a=c&&c[a];return a!=null?a:b};function ba(a){r.setTimeout(()=>{throw a;},0)};var ca=aa(748402147,aa(1,!0));function v(a){v[" "](a);return a}v[" "]=function(){};var da={},w=null;let ea=void 0;function x(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var y=x(),C=x("m_m",!0);const F=x("jas",!0);var fa;const ha=[];ha[F]=7;fa=Object.freeze(ha);var G={};function H(a,b){return b===void 0?a.g!==I&&!!(2&(a.j[F]|0)):!!(2&b)&&a.g!==I}const I={};const ia=BigInt(Number.MIN_SAFE_INTEGER),ja=BigInt(Number.MAX_SAFE_INTEGER);function ka(a){if(typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a};function ma(a){return a};function J(a,b,c,d){var f=d!==void 0;d=!!d;const e=[];var g=a.length;let k,h=**********,m=!1;const l=!!(b&64),n=l?b&128?0:-1:void 0;b&1||(k=g&&a[g-1],k!=null&&typeof k==="object"&&k.constructor===Object?(g--,h=g):k=void 0,!l||b&128||f||(m=!0,h=(na??ma)(h-n,n,a,k,void 0)+n));b=void 0;for(f=0;f<g;f++){let p=a[f];if(p!=null&&(p=c(p,d))!=null)if(l&&f>=h){const q=f-n;(b??(b={}))[q]=p}else e[f]=p}if(k)for(let p in k){a=k[p];if(a==null||(a=c(a,d))==null)continue;g=+p;let q;l&&!Number.isNaN(g)&&(q=g+n)<h? e[q]=a:(b??(b={}))[p]=a}b&&(m?e.push(b):e[h]=b);return e}function oa(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ia&&a<=ja?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[F]|0;return a.length===0&&b&1?void 0:J(a,b,oa)}if(a!=null&&a[C]===G)return K(a);return}return a}let na;function K(a){a=a.j;return J(a,a[F]|0,oa)};function pa(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[F]|0;if(ca&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&qa();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[F]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var f=d-1;d=c[f];if(d!=null&&typeof d==="object"&&d.constructor===Object){const e=b&128?0:-1;f-=e;if(f>=1024)throw Error("pvtlmt");for(const g in d){const k=+g;if(k<f)c[k+e]=d[g],delete d[g];else break}b=b&-8380417|(f&1023)<<13}}}a[F]=b|2112;return a} function qa(){if(ca)throw Error("carr");if(y!=null){var a=ea??(ea={});var b=a[y]||0;b>=5||(a[y]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",ba(a))}};function ra(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=sa(a,c,!1,b&&!(c&16)):(a[F]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[C]===G){c=a.j;const d=c[F]|0;H(a,d)||(d&2?b=!0:!(d&32)||d&4096?b=!1:(c[F]=d|2,a.g=I,b=!0),b?(a=new a.constructor(c),a.i=I):a=sa(c,d));return a}}function sa(a,b,c,d){d??(d=!!(34&b));a=J(a,b,ra,d);d=32;c&&(d|=2);b=b&8380609|d;a[F]=b;return a} function ta(a){if(a.g===I){var b=a.j;b=sa(b,b[F]|0);b[F]|=2048;a.j=b;a.g=void 0;a.i=void 0;b=!0}else b=!1;if(!b&&H(a,a.j[F]|0))throw Error();};function ua(a,b,c){ta(a);const d=a.j;va(d,d[F]|0,b,c);return a}function va(a,b,c,d){const f=c+-1;var e=a.length-1;if(e>=0&&f>=e){const g=a[e];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(f<=e)return a[f]=d,b;d!==void 0&&(e=(b??(b=a[F]|0))>>13&1023||536870912,c>=e?d!=null&&(a[e+-1]={[c]:d}):a[f]=d);return b}function L(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return ua(a,b,c)};var M=class{constructor(a){this.j=pa(a)}toJSON(){return K(this)}};M.prototype[C]=G;M.prototype.toString=function(){return this.j.toString()};var N=class{constructor(a,b=!1){this.key=a;this.defaultValue=b;this.valueType="boolean"}},wa=class{constructor(a){this.key=a;this.defaultValue=0;this.valueType="number"}};var xa=new N("45368259"),ya=new N("45357156",!0),za=new N("45350890"),Aa=new N("45414892"),Ba=new N("45620832"),Ca=new N("45648564"),Da=new wa("45711101"),Ea=new wa("45711102");const Fa=RegExp("ad\\\\.doubleclick\\\\.net/(ddm/trackimp|pcs/view)");var O=(a,b)=>a.substring(a.length-7)=="&adurl="?a.substring(0,a.length-7)+b+"&adurl=":a+b;function Ga(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Ha(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Ia(a=document){return a.createElement("img")};function P(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function Ja(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Ka(a,b,c=null,d=!1,f=!1){return Pa(a,b,c,d,f)}function Pa(a,b,c,d,f=!1){a.google_image_requests||(a.google_image_requests=[]);const e=Ia(a.document);if(c||d){const g=k=>{c&&c(k);if(d){k=a.google_image_requests;const h=Array.prototype.indexOf.call(k,e,void 0);h>=0&&Array.prototype.splice.call(k,h,1)}Ja(e,"load",g);Ja(e,"error",g)};P(e,"load",g);P(e,"error",g)}f&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e);return e} function Qa(a,b=!1){var c=window;if(c.fetch){const d={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};b&&(d.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?d.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:d.headers={"Attribution-Reporting-Eligible":"event-source"});return c.fetch(a,d)}return Ka(c,a,void 0,!1,b)};async function Ra(a){if(a.g!==a.A){a.g++;try{const b=Qa(a.v,a.u);if(b instanceof HTMLImageElement)return await new Promise((d,f)=>{b.addEventListener("load",()=>{d()});b.addEventListener("error",()=>{f(Error(""))})}),b;const c=await b;if(c.status===999)throw Error("");return c}catch(b){return await new Promise(c=>void setTimeout(c,a.o)),await Ra(a)}}}var Sa=class{constructor(a){this.g=0;this.v=a.v;this.u=a.u??!1;this.o=a.o??1E3;this.A=a.A??5}};let Ta=0;function Ua(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)};function Q(a,b){a=a.g[b.key];if(b.valueType==="proto"){try{const c=JSON.parse(a);if(Array.isArray(c))return c}catch(c){}return b.defaultValue}return typeof a===typeof b.defaultValue?a:b.defaultValue}var Va=class{constructor(){this.g={}}};function S(){Wa||(Wa=new Xa);return Wa}var Xa=class extends Va{constructor(){super();var a=Ua(Ta,document.currentScript);a=a&&a.getAttribute("data-jc-flags")||"";try{const b=JSON.parse(a)[0];a="";for(let c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^"\\u0003\\u0007\\u0003\\u0007\\b\\u0004\\u0004\\u0006\\u0005\\u0003".charCodeAt(c%10));this.g=JSON.parse(a)}catch(b){}}},Wa;var Ya=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function Za(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\\\d+(?:.|\\n)*)\\\\2"),"$1");b=a.replace(RegExp("\\n *","g"),"\\n");break a}catch(d){b=c;break a}b=void 0}return b};const $a=RegExp("^https?://(\\\\w|-)+\\\\.cdn\\\\.ampproject\\\\.(net|org)(\\\\?|/|$)");var ab=class{constructor(a,b){this.g=a;this.i=b}},bb=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let T=null;function cb(){const a=r.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function db(){const a=r.performance;return a&&a.now?a.now():null};var eb=class{constructor(a,b){var c=db()||cb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const U=r.performance,fb=!!(U&&U.mark&&U.measure&&U.clearMarks),V=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=fb){var b;a=window;if(T===null){T="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(T=(b=c.match(/\\bdeid=([\\d,]+)/))?b[1]:"")}catch(c){}}b=T;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function gb(a){a&&U&&V()&&(U.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),U.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function W(a,b){const c={};c[a]=b;return[c]}function hb(a,b,c,d,f){const e=[];Ha(a,(g,k)=>{(g=ib(g,b,c,d,f))&&e.push(`${k}=${g}`)});return e.join(b)} function ib(a,b,c,d,f){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const e=[];for(let g=0;g<a.length;g++)e.push(ib(a[g],b,c,d+1,f));return e.join(c[d])}}else if(typeof a==="object")return f||(f=0),f<2?encodeURIComponent(hb(a,b,c,d,f+1)):"...";return encodeURIComponent(String(a))}function jb(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.l.length-1} function kb(a,b){let c="https://pagead2.googlesyndication.com"+b,d=jb(a)-b.length;if(d<0)return"";a.g.sort((e,g)=>e-g);b=null;let f="";for(let e=0;e<a.g.length;e++){const g=a.g[e],k=a.i[g];for(let h=0;h<k.length;h++){if(!d){b=b==null?g:b;break}let m=hb(k[h],a.l,",$");if(m){m=f+m;if(d>=m.length){d-=m.length;c+=m;f=a.l;break}b=b==null?g:b}}}a="";b!=null&&(a=`${f}${"trn"}=${b}`);return c+a}var lb=class{constructor(){this.l="&";this.i={};this.m=0;this.g=[]}};var mb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\\\\\/?#]*)@)?([^\\\\\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\\\\\/?#]|$))?([^?#]+)?(?:\\\\?([^#]*))?(?:#([\\\\s\\\\S]*))?$");function nb(a,b,c,d){const f=c.length;for(;(b=a.indexOf(c,b))>=0&&b<d;){var e=a.charCodeAt(b-1);if(e==38||e==63)if(e=a.charCodeAt(b+f),!e||e==61||e==38||e==35)return b;b+=f+1}return-1}var ob=/#|$/; function pb(a){const b=a.search(ob);let c=nb(a,0,"ase",b);if(c<0)return null;let d=a.indexOf("&",c);if(d<0||d>b)d=b;return decodeURIComponent(a.slice(c+4,d!==-1?d:0).replace(/\\+/g," "))}var qb=/[?&]($|#)/; function rb(a,b){var c=a.search(ob),d=0,f;const e=[];for(;(f=nb(a,d,"nis",c))>=0;)e.push(a.substring(d,f)),d=Math.min(a.indexOf("&",f)+1||c,c);e.push(a.slice(d));a=e.join("").replace(qb,"$1");(b="nis"+(b!=null?"="+encodeURIComponent(String(b)):""))?(c=a.indexOf("#"),c<0&&(c=a.length),d=a.indexOf("?"),d<0||d>c?(d=c,f=""):f=a.substring(d+1,c),a=[a.slice(0,d),f,a.slice(c)],c=a[1],a[1]=b?c?c+"&"+b:b:c,b=a[0]+(a[1]?"?"+a[1]:"")+a[2]):b=a;return b};function sb(a,b,c,d){let f,e;try{a.g&&a.g.g?(e=a.g.start(b.toString(),3),f=c(),a.g.end(e)):f=c()}catch(g){c=!0;try{gb(e),c=a.D(b,new Ya(g,{message:Za(g)}),void 0,d)}catch(k){a.m(217,k)}if(c)window.console?.error?.(g);else throw g;}return f}function tb(a,b,c,d){var f=X;return(...e)=>sb(f,a,()=>b.apply(c,e),d)} var vb=class{constructor(a=null){this.C=Y;this.g=a;this.i=null;this.l=!1;this.D=this.m}m(a,b,c,d,f){f=f||"jserror";let e=void 0;try{const D=new lb;var g=D;g.g.push(1);g.i[1]=W("context",a);b.error&&b.meta&&b.id||(b=new Ya(b,{message:Za(b)}));g=b;if(g.msg){b=D;var k=g.msg.substring(0,512);b.g.push(2);b.i[2]=W("msg",k)}var h=g.meta||{};k=h;if(this.i)try{this.i(k)}catch(B){}if(d)try{d(k)}catch(B){}d=D;h=[h];d.g.push(3);d.i[3]=h;var m;if(!(m=q)){d=r;h=[];let B;k=null;do{var l=d;try{var n;if(n=!!l&&l.location.href!= null)b:{try{v(l.foo);n=!0;break b}catch(z){}n=!1}var p=n}catch{p=!1}p?(B=l.location.href,k=l.document&&l.document.referrer||null):(B=k,k=null);h.push(new bb(B||""));try{d=l.parent}catch(z){d=null}}while(d&&l!==d);for(let z=0,La=h.length-1;z<=La;++z)h[z].depth=La-z;l=r;if(l.location&&l.location.ancestorOrigins&&l.location.ancestorOrigins.length===h.length-1)for(p=1;p<h.length;++p){const z=h[p];z.url||(z.url=l.location.ancestorOrigins[p-1]||"",z.g=!0)}m=h}var q=m;let R=new bb(r.location.href,!1);m= null;const la=q.length-1;for(l=la;l>=0;--l){var t=q[l];!m&&$a.test(t.url)&&(m=t);if(t.url&&!t.g){R=t;break}}t=null;const yb=q.length&&q[la].url;R.depth!==0&&yb&&(t=q[la]);e=new ab(R,t);if(e.i){q=D;var u=e.i.url||"";q.g.push(4);q.i[4]=W("top",u)}var E={url:e.g.url||""};if(e.g.url){const B=e.g.url.match(mb);var A=B[1],Ma=B[3],Na=B[4];u="";A&&(u+=A+":");Ma&&(u+="//",u+=Ma,Na&&(u+=":"+Na));var Oa=u}else Oa="";A=D;E=[E,{url:Oa}];A.g.push(5);A.i[5]=E;ub(this.C,f,D,this.l,c)}catch(D){try{ub(this.C,f,{context:"ecmserr", rctx:a,msg:Za(D),url:e?.g.url??""},this.l,c)}catch(R){}}return!0}};class wb{};function ub(a,b,c,d=!1,f,e){if((d?a.g:Math.random())<(f||.01))try{let g;c instanceof lb?g=c:(g=new lb,Ha(c,(h,m)=>{var l=g;const n=l.m++;h=W(m,h);l.g.push(n);l.i[n]=h}));const k=kb(g,"/pagead/gen_204?id="+b+"&");k&&(typeof e!=="undefined"?Ka(r,k,e):Ka(r,k))}catch(g){}}function xb(){var a=Y,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var zb=class{constructor(){this.g=Math.random()}};let Y,X; const Z=new class{constructor(a,b){this.i=[];this.l=b||r;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.i=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=V()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new eb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;U&&V()&&U.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(db()||cb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;U&&V()&&U.mark(b);!this.g||this.i.length> 2048||this.i.push(a)}}}(1,window);function Ab(){window.google_measure_js_timing||(Z.g=!1,Z.i!==Z.l.google_js_reporting_queue&&(V()&&Array.prototype.forEach.call(Z.i,gb,void 0),Z.i.length=0))} (function(a){Y=a??new zb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());xb();X=new vb(Z);X.i=b=>{var c=Ta;c!==0&&(b.jc=String(c),c=(c=Ua(c,document.currentScript))&&c.getAttribute("data-jc-version")||"unknown",b.shv=c)};X.l=!0;window.document.readyState==="complete"?Ab():Z.g&&P(window,"load",()=>{Ab()})})();function Bb(a,b,c,d){return tb(a,b,c,d)} function Cb(a,b,c,d){var f=wb;var e="B";f.B&&f.hasOwnProperty(e)||(e=new f,f.B=e);f=[];!b.eid&&f.length&&(b.eid=f.toString());ub(Y,a,b,!0,c,d)};function Db(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b};function Eb(){const a=window;if(a.gmaSdk||a.webkit?.messageHandlers?.getGmaViewSignals)return a;try{const b=window.parent;if(b.gmaSdk||b.webkit?.messageHandlers?.getGmaViewSignals)return b}catch(b){}return null} function Fb(a,b={},c=()=>{},d=()=>{},f=200,e,g){const k=String(Math.floor(Ga()*2147483647));let h=0;const m=l=>{try{const n=typeof l.data==="object"?l.data:JSON.parse(l.data);k===n.paw_id&&(window.clearTimeout(h),window.removeEventListener("message",m),n.signal?c(n.signal):n.error&&d(n.error))}catch(n){g("paw_sigs",{msg:"postmessageError",err:n instanceof Error?n.message:"nonError",data:l.data==null?"null":l.data.length>500?l.data.substring(0,500):l.data})}};window.addEventListener("message",l=>{e(903, ()=>{m(l)})()});a.postMessage({paw_id:k,...b});h=window.setTimeout(()=>{window.removeEventListener("message",m);d("PAW GMA postmessage timed out.")},f)};function Gb(a=document){return!!a.featurePolicy?.allowedFeatures().includes("attribution-reporting")};var Hb=class extends M{};function Ib(a,b){return L(a,2,b)}function Jb(a,b){return L(a,3,b)}function Kb(a,b){return L(a,4,b)}function Lb(a,b){return L(a,5,b)}function Mb(a,b){return L(a,9,b)} function Nb(a,b){{var c=b;ta(a);const l=a.j;b=l[F]|0;if(c==null)va(l,b,10);else{var d=c===fa?7:c[F]|0,f=d,e=!!(2&d)&&!!(4&d)||!!(256&d),g=e||Object.isFrozen(c),k=!0,h=!0;for(let n=0;n<c.length;n++){var m=c[n];e||(m=H(m),k&&(k=!m),h&&(h=m))}e||(d=k?13:5,d=h?d&-4097:d|4096);g&&d===f||(c=[...c],f=0,d=2&b?d|2:d&-3,d&=-273);d!==f&&(c[F]=d);b=va(l,b,10,c);2&d||!(4096&d||16&d)||(c=l,b===void 0&&(b=c[F]|0),b&32&&!(b&4096)&&(c[F]=b|4096))}}return a}function Ob(a,b){return ua(a,11,b==null?b:ka(b))} function Pb(a,b){return L(a,1,b)}function Qb(a,b){return ua(a,7,b==null?b:ka(b))}var Rb=class extends M{};const Sb="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Tb(){var a=window;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Sb).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} function Ub(a){return Ob(Nb(Lb(Ib(Pb(Kb(Qb(Mb(Jb(new Rb,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new Hb;c=L(c,1,b.brand);return L(c,2,b.version)})||[]),a.wow64||!1)}function Vb(){return Tb()?.then(a=>Ub(a))??null};class Wb{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};window.viewReq=[];function Xb(a,b){b?(b=Ia(),b.src=a.replace("&amp;","&"),b.attributionSrc="",window.viewReq.push(b)):(b=new Image,b.src=a.replace("&amp;","&"),window.viewReq.push(b))} function Yb(a,b){const c={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};b&&(c.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?c.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:c.headers={"Attribution-Reporting-Eligible":"event-source"});fetch(a,c).catch(()=>{Xb(a,b)})}function Zb(a,b){if(Q(S(),Ea)){const c=S();Ra(new Sa({v:a,u:b,o:Q(c,Da),A:Q(c,Ea)}))}else window.fetch?Yb(a,b):Xb(a,b)} function $b(){const a=r.document;return new Promise(b=>{const c=Db(a);if(c){var d=()=>{(a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]??0)!==3&&(Ja(a,c,d),b())};P(a,c,d)}})}Ta=42; window.vu=a=>{var b=Q(S(),ya)||Q(S(),Aa);const c=Eb();if(b&&c?.gmaSdk?.getViewSignals){var d=c.gmaSdk.getViewSignals();d&&!Q(S(),Aa)&&(a=O(a,"&ms="+d))}Q(S(),xa)&&"__google_lidar_radf_"in window&&(a=O(a,"&avradf=1"));const f=[];d=()=>{const k=new Wb;f.push(k.promise);return k.resolve};if(Q(S(),Ca)){var e=$b();if(e!=null){const k=d();e.then(()=>{a=O(a,"&sbtr=1");k()})}}Q(S(),Ba)&&(a=O(a,"&sbtr=1"));if(Q(S(),za)&&(e=Vb(),e!=null)){const k=d();e.then(h=>{var m=JSON.stringify(K(h));h=[];var l=0;for(var n= 0;n<m.length;n++){var p=m.charCodeAt(n);p>255&&(h[l++]=p&255,p>>=8);h[l++]=p}m=3;m===void 0&&(m=0);if(!w)for(w={},l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),n=["+/=","+/","-_=","-_.","-_"],p=0;p<5;p++){var q=l.concat(n[p].split(""));da[p]=q;for(var t=0;t<q.length;t++){var u=q[t];w[u]===void 0&&(w[u]=t)}}m=da[m];l=Array(Math.floor(h.length/3));n=m[64]||"";for(p=q=0;q<h.length-2;q+=3){var E=h[q],A=h[q+1];u=h[q+2];t=m[E>>2];E=m[(E&3)<<4|A>>4];A=m[(A&15)<<2|u>>6];u=m[u& 63];l[p++]=t+E+A+u}t=0;u=n;switch(h.length-q){case 2:t=h[q+1],u=m[(t&15)<<2]||n;case 1:h=h[q],l[p]=m[h>>2]+m[(h&3)<<4|t>>4]+u+n}h=l.join("");h.length>0&&(a=O(a,"&uach="+h));k()})}if(b&&c?.webkit?.messageHandlers?.getGmaViewSignals){const k=d();Fb(c.webkit.messageHandlers.getGmaViewSignals,{},h=>{Q(S(),Aa)||(a=O(a,"&"+h));k()},()=>{k()},200,Bb,Cb)}const g=pb(a)===(2).toString()||Fa.test(a);g&&(b=Gb(window.document)?6:5,a=rb(a,b));f.length>0?Promise.all(f).then(()=>{Zb(a,g)}):Zb(a,g)};}).call(this);</script><script>vu("https://securepubads.g.doubleclick.net/pcs/view?xai\\x3dAKAOjstg4s35zpDjQyWRpv2j82xbGSOpyguEdP4ff0uaBlSlG67Pem3_E26jXkuofJuk7_tWekruYV2zozUW7eSWiUBODixAla8jJC6a0QUnTwa76jeIaZbAUCCPCNjtmwJHgyOljrLpo1OzHzfATlUqgmp5JILosTDmCovEEFQcrM3o3c2YWhZpseq-QARzvmyfP-Qnpg5ULT2t-BW7u9CEqDz-h9jfIsfT9ANDNf8a9lRBMuZff5dL8NO70eJg0HYjlZfkkyWjWHSAziEirMt3LvjxjjHypuYVsXcA7vmNUF3uzTH1_nMpuvdxD63rympELNHGiosm1vCsJbxdlZd3aFciECYBiTU8D9kuWrJTHZCN78c1fMVAboHX6LkLFJNKyviphyEFw1iIbqyn0IVxkMT4C-3lDlGkxW2RW6ziODO1WtCviPgPSk1nmw\\x26sai\\x3dAMfl-YR6gMXeQDLEza5AmWaGFKJlAECvTsgn0PZ2OQhOsJiKQM6tAwx9FRWiLAX5Tsrn8--QOLRBSFOuoM4c_UNUpocbyBtZANatGW-YEvOneDxOgbH2wXS3ldAzQ70PiR1WYvikK3XiWlww6iyAqgEqYRWLI0q5bHh2slIIeasGf_jdr_CDsK2oDDcBPrGF2ePZn9Lv0aHKn5S-jdN-PIpI_N_kaRLxg5OCtILFf7jQrHuYMg7BUibt-xVloLWj-9TBi2A\\x26sig\\x3dCg0ArKJSzOJTNzZdoRHvEAE\\x26uach_m\\x3d%5BUACH%5D\\x26urlfix\\x3d1\\x26adurl\\x3d")</script><div class="GoogleActiveViewInnerContainer"id="avic_CP-_toe6_o4DFQ88RAgdQD0jwQ"style="left:0px;top:0px;width:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;"></div><div style="display:inline"class="GoogleActiveViewElement"data-google-av-cxn="https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjsvVLjFoyK5WSsrjLwACsTdVE_9UwqCWBEzQPqqOJ_0qjD4L_8maM7LD-Zp6E5yFIbSuhsra2ROJXjtf0-x4n-kzQkywP5N3UVGmMN6fQqPEDSWKbIEEhANDd0AC0PRbPspDsCe9IF_GAxWasbgs95EJOpYbQgVfrvN7dvvKeJ0IZU2r8aA&amp;sig=Cg0ArKJSzLMwuMqG_d7KEAE"data-google-av-adk="2159526670"data-google-av-metadata="la=0&amp;xdi=0&amp;"data-google-av-ufs-integrator-metadata="Co8BCkNtb2RlbF9wZXJzb25fY291bnRyeV9jb2RlX01YX3BlcnNvbl9yZWdpb25fY29kZV80ZDU4MmQ0YzQ1NGY0ZS5qc29uEhpDUC1fdG9lNl9vNERGUTg4UkFnZFFEMGp3URgBIhEItBMgtBMoAjACOAFdzcxMPii70dS5-_____8BMLvR1LkDOANAAUgAUAESmQIKjAJodHRwczovL3BhZ2VhZDIuZ29vZ2xlc3luZGljYXRpb24uY29tL3Bjcy9hY3RpdmV2aWV3P3hhaT1BS0FPanN2VkxqRm95SzVXU3Nyakx3QUNzVGRWRV85VXdxQ1dCRXpRUHFxT0pfMHFqRDRMXzhtYU03TEQtWnA2RTV5RkliU3Voc3JhMlJPSlhqdGYwLXg0bi1relFreXdQNU4zVVZHbU1ONmZRcVBFRFNXS2JJRUVoQU5EZDBBQzBQUmJQc3BEc0NlOUlGX0dBeFdhc2Jnczk1RUpPcFliUWdWZnJ2TjdkdnZLZUowSVpVMnI4YUEmc2lnPUNnMEFyS0pTekxNd3VNcUdfZDdLRUFFEgAaACABKAAwBBoeChpDUC1fdG9lNl9vNERGUTg4UkFnZFFEMGp3URAF"data-google-av-override="-1"data-google-av-dm="2"data-google-av-aid="0"data-google-av-naid="1"data-google-av-slift=""data-google-av-cpmav=""data-google-av-btr="https://securepubads.g.doubleclick.net/pcs/view?xai=AKAOjstM2z-PW66ZF897HkXwgfZpd5FOVZKeanVaJsHtr530eWi9eo03DQLURcF_cpCqKmHXTEipimxekYoFjicWKF_TUkxOiJrjtKc3w0namSJdos8dkQgGapvOuV_6DluA94RE2KijcKbBW13Fma9sKrr2UGz7r8YTvvApE9IQq49SlL5_tkZDtOsyPR-9BgvmrL0lT5KKKPS2XExPNZnMR0qvKavqfO-FjizwkPtTjLHpS8I0oR171VYtTbBhhlq_Kn6REsteUNDBeki2h0lsa-nlvaicEL3AVQ1eNkD05Q1KoOayxAQ2DoqB3LE-PKw1OOuCn0tW7PIBrv2hds8bL11Ksl08JK94iV_enZdEC_lCqRZusVm9bZSxr9i0RJsd98gxoE_mxcMjZxZ8psW-d-sKpSkH9-O1JXKctLyk3l4VLlnxQtZvy_kdxQ2h&amp;sai=AMfl-YQCfRhy5DHFG9VdgqH51hy0K6NehnIBaGYiHnBMdqF4r6j074K1suFsYe6AybDnPKh_TySsHNMG_gJtzBVaNTf9Garca5BBM2SA95TyvdnB20a86VSru7q1ZEiQQ5SFJ5J3d2s6TBRzBJ_tzP7w3EPpuYlfKByBCmd5M2R114tciPpjICrD6OJQ94Z9HpqOVxDtddTaQWlthRp48CXdnOXPayM1WcOdc8Ew12L5nv1fd0O6aTNwGYhzXyaYIJrpeWA&amp;sig=Cg0ArKJSzLijeVwCprW5EAE&amp;uach_m=%5BUACH%5D&amp;urlfix=1&amp;adurl="data-google-av-itpl="19"data-google-av-rs="4"data-google-av-flags="[&quot;x%278440&#39;9efotm(&amp;753374%2bejvf/%27844&gt;&#39;9wuvb$&amp;56533&gt;!=|vqc)!273794&amp;&lt;qqvb/%&lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;&lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&#39;76463;21$?ebkpb$&amp;0366717&gt;*&gt;bgipf+!3=712363%9aihwc)!7202&lt;217&#39;9efotm(&amp;20061;48&amp;&gt;`dopb/%&lt;1707200!=8(&amp;2005575?&amp;&gt;`dopb/%&lt;170642?!=|vqc)!7201;=50&#39;9wuvb$&amp;03641654*&gt;bgipf+!3=731103%9aihwc)!7200?073&#39;9efotm(&amp;2004?51;&amp;&gt;`dopb/%&lt;17&gt;474&gt;!=nehu`/!36406412!9abk{a($167745;=&amp;&lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&gt;7&amp;&lt;qqvb/%&lt;104=460!=nehu`/!363;42&gt;7!9abk{a($1656;3?&lt;&amp;&lt;cbotf+*01011776%2bejvf/%72&gt;17266!=efdwa*&#39;7616?=&lt;=$?ebkpb$&amp;0335225&gt;*&gt;bgipf+!3=340764%94&gt;44653~&quot;]"><script>var amzn_win=window,amzn_c=5,amzn_x=0;while(amzn_x<amzn_c){amzn_win=amzn_win.parent;if(amzn_win.apstag)try{amzn_win.apstag.renderImp(document,"JGyTikYr9I7p-VtjqAElD_sAAAGYkEnuZAYAAAJYAQBhcHNfdHhuX2JpZDEgICBhcHNfdHhuX2ltcDEgICDdcNlI");amzn_x=amzn_c}catch(e){}amzn_x++};</script></div><script id="googleActiveViewDisplayScript" src="https://pagead2.googlesyndication.com/pagead/managed/js/activeview/current/ufs_web_display.js"></script><script type="text/javascript">osdlfm();</script><div style="bottom:0;right:0;width:100px;height:100px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAAB5SURBVBjTbVCJDQAxCNIN2H/aiwrUNmfTx4gUifiPnKhXLWfoKvhQ1gghfVYFANFw5roOo8UNLG7+nY8SRfNu3WE89W9ws7sjZ9DGZM4emAVzCKjhKLmZQ8Ts2LrTutaUdqav46BFPQ62Avqy/A7/cHMjYG4pye34BxTTBcI9/YzhAAAAAElFTkSuQmCC') !important;"></div><script data-jc="103" data-jc-version="r20250807" data-jcp-base_url="https://googleads.g.doubleclick.net/pagead/conversion/?ai=&amp;sigh=BpnfxIaauQU" data-jcp-cpu_label="heavy_ad_intervention_cpu" data-jcp-net_label="heavy_ad_intervention_network">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function n(a,b){a:{var e=["CLOSURE_FLAGS"];for(var c=h,d=0;d<e.length;d++)if(c=c[e[d]],c==null){e=null;break a}e=c}a=e&&e[a];return a!=null?a:b};function p(a){h.setTimeout(()=>{throw a;},0)};var u=n(748402147,n(1,!0));let v=void 0;function w(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var x=w(),y=w("m_m",!0);const z=w("jas",!0);var A={};function B(a,b){return b===void 0?a.h!==C&&!!(2&(a.g[z]|0)):!!(2&b)&&a.h!==C}const C={};const D=BigInt(Number.MIN_SAFE_INTEGER),E=BigInt(Number.MAX_SAFE_INTEGER);function F(a){return a};function G(a,b,e,c){var d=c!==void 0;c=!!c;const l=[];var f=a.length;let g,k=**********,J=!1;const r=!!(b&64),q=r?b&128?0:-1:void 0;b&1||(g=f&&a[f-1],g!=null&&typeof g==="object"&&g.constructor===Object?(f--,k=f):g=void 0,!r||b&128||d||(J=!0,k=(H??F)(k-q,q,a,g,void 0)+q));b=void 0;for(d=0;d<f;d++){let m=a[d];if(m!=null&&(m=e(m,c))!=null)if(r&&d>=k){const t=d-q;(b??(b={}))[t]=m}else l[d]=m}if(g)for(let m in g){a=g[m];if(a==null||(a=e(a,c))==null)continue;f=+m;let t;r&&!Number.isNaN(f)&&(t=f+q)<k?l[t]= a:(b??(b={}))[m]=a}b&&(J?l.push(b):l[k]=b);return l}function I(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=D&&a<=E?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[z]|0;return a.length===0&&b&1?void 0:G(a,b,I)}if(a!=null&&a[y]===A)return K(a);return}return a}let H;function K(a){a=a.g;return G(a,a[z]|0,I)};function L(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[z]|0;if(u&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&M();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[z]=b|2048),a;var e=a;b|=64;var c=e.length;if(c){var d=c-1;c=e[d];if(c!=null&&typeof c==="object"&&c.constructor===Object){const l=b&128?0:-1;d-=l;if(d>=1024)throw Error("pvtlmt");for(const f in c){const g=+f;if(g<d)e[g+l]=c[f],delete c[f];else break}b=b&-8380417|(d&1023)<<13}}}a[z]=b|2112;return a} function M(){if(u)throw Error("carr");if(x!=null){var a=v??(v={});var b=a[x]||0;b>=5||(a[x]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",p(a))}};function N(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var e=a[z]|0;a.length===0&&e&1?a=void 0:e&2||(!b||4096&e||16&e?a=O(a,e,!1,b&&!(e&16)):(a[z]|=34,e&4&&Object.freeze(a)));return a}if(a!=null&&a[y]===A){e=a.g;const c=e[z]|0;B(a,c)||(c&2?b=!0:c&32&&!(c&4096)?(e[z]=c|2,a.h=C,b=!0):b=!1,b?(a=new a.constructor(e),a.m=C):a=O(e,c));return a}}function O(a,b,e,c){c??(c=!!(34&b));a=G(a,b,N,c);c=32;e&&(c|=2);b=b&8380609|c;a[z]=b;return a};function P(a,b,e){if(e!=null&&typeof e!=="string")throw Error();if(a.h===C){var c=a.g;c=O(c,c[z]|0);c[z]|=2048;a.g=c;a.h=void 0;a.m=void 0;c=!0}else c=!1;if(!c&&B(a,a.g[z]|0))throw Error();a=a.g;a:{var d=a[z]|0;c=b+-1;const l=a.length-1;if(l>=0&&c>=l){const f=a[l];if(f!=null&&typeof f==="object"&&f.constructor===Object){f[b]=e;break a}}c<=l?a[c]=e:e!==void 0&&(d=(d??a[z]|0)>>13&1023||536870912,b>=d?e!=null&&(a[d+-1]={[b]:e}):a[c]=e)}};var Q=class{constructor(a){this.g=L(a)}toJSON(){return K(this)}};Q.prototype[y]=A;Q.prototype.toString=function(){return this.g.toString()};var R=class extends Q{};function S(a=window){return a};var T=/#|$/;const U=function(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)}(103,document.currentScript);if(U==null)throw Error("JSC not found 103");const V={},W=U.attributes;for(let a=W.length-1;a>=0;a--){const b=W[a].name;b.indexOf("data-jcp-")===0&&(V[b.substring(9)]=W[a].value)} (function(a,b,e){var c=window;a&&b&&e&&c.ReportingObserver&&c.fetch&&(new c.ReportingObserver((d,l)=>{d=d[0];if(d?.body?.id==="HeavyAdIntervention"){d=(d.body.message?.indexOf("network")||0)>0?e:b;var f=a.search(T);var g;b:{for(g=0;(g=a.indexOf("ad_signals",g))>=0&&g<f;){var k=a.charCodeAt(g-1);if(k==38||k==63)if(k=a.charCodeAt(g+10),!k||k==61||k==38||k==35)break b;g+=11}g=-1}k=g;if(k<0)f=null;else{g=a.indexOf("&",k);if(g<0||g>f)g=f;f=decodeURIComponent(a.slice(k+11,g!==-1?g:0).replace(/\\+/g," "))}f? (navigator.sendBeacon("https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&label="+d),d={i:f,label:d},f=new R,d!=null&&(d.i!=null&&P(f,1,d.i),d.u!=null&&P(f,3,d.u),d.label!=null&&P(f,6,d.label),d.l!=null&&P(f,7,d.l),d.j!=null&&P(f,8,d.j),d.o!=null&&P(f,11,d.o)),S(h).fence?.reportEvent({eventType:"interaction",eventData:JSON.stringify(K(f)),destination:["buyer"]})):c.fetch(`${a}&label=${d}`,{keepalive:!0,method:"get",mode:"no-cors"});l.disconnect()}},{types:["intervention"], buffered:!0})).observe()})(V.base_url,V.cpu_label,V.net_label);}).call(this);</script></body></html>
{"/15184186/time.is_homepage_300x250_2":["html",1,null,null,1,1,1,0,0,null,null,null,1,null,[138216031689],[4478640886],[4468582735],[2178672843],null,null,null,null,null,null,null,1,null,null,null,null,null,null,"AOrYGsmWihAf_k2daFkK7I58ZAodAgqtK53G399Tv-xH1hMek6RDO7AP-TWneLGjt27ZPWScF-s5Wq-PXfGOhQ4d7bae7TDzwalGS4OfRbVgLid5jMg","CIDAtoe6_o4DFQ88RAgdQD0jwQ",null,null,null,null,null,null,null,null,null,null,null,null,null,null,"5",null,null,null,null,null,null,null,null,null,null,null,null,null,null,1]}
<!doctype html><html><head><script>var inDapIF=true,inGptIF=true;</script></head><body leftMargin="0" topMargin="0" marginwidth="0" marginheight="0"><script>window.dicnf = {};</script><script data-jc="42" data-jc-version="r20250807" data-jc-flags="[&quot;x%278446&#39;9efotm(&amp;20067;&gt;8&amp;&gt;`dopb/%&lt;1732261!=|vqc)!7201061?&#39;9efotm(&amp;20723;&gt;:&amp;&gt;`dopb/%&lt;1245;05!=nehu`/!361&lt;&lt;101!9abk{a($16463496&amp;&lt;qqvb/%&lt;1374236!=8(&amp;2042627:&amp;&gt;6x&quot;]">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var r=this||self;function aa(a,b){a:{var c=["CLOSURE_FLAGS"];for(var d=r,f=0;f<c.length;f++)if(d=d[c[f]],d==null){c=null;break a}c=d}a=c&&c[a];return a!=null?a:b};function ba(a){r.setTimeout(()=>{throw a;},0)};var ca=aa(748402147,aa(1,!0));function v(a){v[" "](a);return a}v[" "]=function(){};var da={},w=null;let ea=void 0;function x(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var y=x(),C=x("m_m",!0);const F=x("jas",!0);var fa;const ha=[];ha[F]=7;fa=Object.freeze(ha);var G={};function H(a,b){return b===void 0?a.g!==I&&!!(2&(a.j[F]|0)):!!(2&b)&&a.g!==I}const I={};const ia=BigInt(Number.MIN_SAFE_INTEGER),ja=BigInt(Number.MAX_SAFE_INTEGER);function ka(a){if(typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a};function ma(a){return a};function J(a,b,c,d){var f=d!==void 0;d=!!d;const e=[];var g=a.length;let k,h=**********,m=!1;const l=!!(b&64),n=l?b&128?0:-1:void 0;b&1||(k=g&&a[g-1],k!=null&&typeof k==="object"&&k.constructor===Object?(g--,h=g):k=void 0,!l||b&128||f||(m=!0,h=(na??ma)(h-n,n,a,k,void 0)+n));b=void 0;for(f=0;f<g;f++){let p=a[f];if(p!=null&&(p=c(p,d))!=null)if(l&&f>=h){const q=f-n;(b??(b={}))[q]=p}else e[f]=p}if(k)for(let p in k){a=k[p];if(a==null||(a=c(a,d))==null)continue;g=+p;let q;l&&!Number.isNaN(g)&&(q=g+n)<h? e[q]=a:(b??(b={}))[p]=a}b&&(m?e.push(b):e[h]=b);return e}function oa(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ia&&a<=ja?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[F]|0;return a.length===0&&b&1?void 0:J(a,b,oa)}if(a!=null&&a[C]===G)return K(a);return}return a}let na;function K(a){a=a.j;return J(a,a[F]|0,oa)};function pa(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[F]|0;if(ca&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&qa();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[F]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var f=d-1;d=c[f];if(d!=null&&typeof d==="object"&&d.constructor===Object){const e=b&128?0:-1;f-=e;if(f>=1024)throw Error("pvtlmt");for(const g in d){const k=+g;if(k<f)c[k+e]=d[g],delete d[g];else break}b=b&-8380417|(f&1023)<<13}}}a[F]=b|2112;return a} function qa(){if(ca)throw Error("carr");if(y!=null){var a=ea??(ea={});var b=a[y]||0;b>=5||(a[y]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",ba(a))}};function ra(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=sa(a,c,!1,b&&!(c&16)):(a[F]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[C]===G){c=a.j;const d=c[F]|0;H(a,d)||(d&2?b=!0:!(d&32)||d&4096?b=!1:(c[F]=d|2,a.g=I,b=!0),b?(a=new a.constructor(c),a.i=I):a=sa(c,d));return a}}function sa(a,b,c,d){d??(d=!!(34&b));a=J(a,b,ra,d);d=32;c&&(d|=2);b=b&8380609|d;a[F]=b;return a} function ta(a){if(a.g===I){var b=a.j;b=sa(b,b[F]|0);b[F]|=2048;a.j=b;a.g=void 0;a.i=void 0;b=!0}else b=!1;if(!b&&H(a,a.j[F]|0))throw Error();};function ua(a,b,c){ta(a);const d=a.j;va(d,d[F]|0,b,c);return a}function va(a,b,c,d){const f=c+-1;var e=a.length-1;if(e>=0&&f>=e){const g=a[e];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(f<=e)return a[f]=d,b;d!==void 0&&(e=(b??(b=a[F]|0))>>13&1023||536870912,c>=e?d!=null&&(a[e+-1]={[c]:d}):a[f]=d);return b}function L(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return ua(a,b,c)};var M=class{constructor(a){this.j=pa(a)}toJSON(){return K(this)}};M.prototype[C]=G;M.prototype.toString=function(){return this.j.toString()};var N=class{constructor(a,b=!1){this.key=a;this.defaultValue=b;this.valueType="boolean"}},wa=class{constructor(a){this.key=a;this.defaultValue=0;this.valueType="number"}};var xa=new N("45368259"),ya=new N("45357156",!0),za=new N("45350890"),Aa=new N("45414892"),Ba=new N("45620832"),Ca=new N("45648564"),Da=new wa("45711101"),Ea=new wa("45711102");const Fa=RegExp("ad\\\\.doubleclick\\\\.net/(ddm/trackimp|pcs/view)");var O=(a,b)=>a.substring(a.length-7)=="&adurl="?a.substring(0,a.length-7)+b+"&adurl=":a+b;function Ga(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Ha(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Ia(a=document){return a.createElement("img")};function P(a,b,c){typeof a.addEventListener==="function"&&a.addEventListener(b,c,!1)}function Ja(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Ka(a,b,c=null,d=!1,f=!1){return Pa(a,b,c,d,f)}function Pa(a,b,c,d,f=!1){a.google_image_requests||(a.google_image_requests=[]);const e=Ia(a.document);if(c||d){const g=k=>{c&&c(k);if(d){k=a.google_image_requests;const h=Array.prototype.indexOf.call(k,e,void 0);h>=0&&Array.prototype.splice.call(k,h,1)}Ja(e,"load",g);Ja(e,"error",g)};P(e,"load",g);P(e,"error",g)}f&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e);return e} function Qa(a,b=!1){var c=window;if(c.fetch){const d={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};b&&(d.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?d.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:d.headers={"Attribution-Reporting-Eligible":"event-source"});return c.fetch(a,d)}return Ka(c,a,void 0,!1,b)};async function Ra(a){if(a.g!==a.A){a.g++;try{const b=Qa(a.v,a.u);if(b instanceof HTMLImageElement)return await new Promise((d,f)=>{b.addEventListener("load",()=>{d()});b.addEventListener("error",()=>{f(Error(""))})}),b;const c=await b;if(c.status===999)throw Error("");return c}catch(b){return await new Promise(c=>void setTimeout(c,a.o)),await Ra(a)}}}var Sa=class{constructor(a){this.g=0;this.v=a.v;this.u=a.u??!1;this.o=a.o??1E3;this.A=a.A??5}};let Ta=0;function Ua(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)};function Q(a,b){a=a.g[b.key];if(b.valueType==="proto"){try{const c=JSON.parse(a);if(Array.isArray(c))return c}catch(c){}return b.defaultValue}return typeof a===typeof b.defaultValue?a:b.defaultValue}var Va=class{constructor(){this.g={}}};function S(){Wa||(Wa=new Xa);return Wa}var Xa=class extends Va{constructor(){super();var a=Ua(Ta,document.currentScript);a=a&&a.getAttribute("data-jc-flags")||"";try{const b=JSON.parse(a)[0];a="";for(let c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^"\\u0003\\u0007\\u0003\\u0007\\b\\u0004\\u0004\\u0006\\u0005\\u0003".charCodeAt(c%10));this.g=JSON.parse(a)}catch(b){}}},Wa;var Ya=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function Za(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\\\d+(?:.|\\n)*)\\\\2"),"$1");b=a.replace(RegExp("\\n *","g"),"\\n");break a}catch(d){b=c;break a}b=void 0}return b};const $a=RegExp("^https?://(\\\\w|-)+\\\\.cdn\\\\.ampproject\\\\.(net|org)(\\\\?|/|$)");var ab=class{constructor(a,b){this.g=a;this.i=b}},bb=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let T=null;function cb(){const a=r.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function db(){const a=r.performance;return a&&a.now?a.now():null};var eb=class{constructor(a,b){var c=db()||cb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const U=r.performance,fb=!!(U&&U.mark&&U.measure&&U.clearMarks),V=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=fb){var b;a=window;if(T===null){T="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(T=(b=c.match(/\\bdeid=([\\d,]+)/))?b[1]:"")}catch(c){}}b=T;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function gb(a){a&&U&&V()&&(U.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),U.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function W(a,b){const c={};c[a]=b;return[c]}function hb(a,b,c,d,f){const e=[];Ha(a,(g,k)=>{(g=ib(g,b,c,d,f))&&e.push(`${k}=${g}`)});return e.join(b)} function ib(a,b,c,d,f){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const e=[];for(let g=0;g<a.length;g++)e.push(ib(a[g],b,c,d+1,f));return e.join(c[d])}}else if(typeof a==="object")return f||(f=0),f<2?encodeURIComponent(hb(a,b,c,d,f+1)):"...";return encodeURIComponent(String(a))}function jb(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.l.length-1} function kb(a,b){let c="https://pagead2.googlesyndication.com"+b,d=jb(a)-b.length;if(d<0)return"";a.g.sort((e,g)=>e-g);b=null;let f="";for(let e=0;e<a.g.length;e++){const g=a.g[e],k=a.i[g];for(let h=0;h<k.length;h++){if(!d){b=b==null?g:b;break}let m=hb(k[h],a.l,",$");if(m){m=f+m;if(d>=m.length){d-=m.length;c+=m;f=a.l;break}b=b==null?g:b}}}a="";b!=null&&(a=`${f}${"trn"}=${b}`);return c+a}var lb=class{constructor(){this.l="&";this.i={};this.m=0;this.g=[]}};var mb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\\\\\/?#]*)@)?([^\\\\\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\\\\\/?#]|$))?([^?#]+)?(?:\\\\?([^#]*))?(?:#([\\\\s\\\\S]*))?$");function nb(a,b,c,d){const f=c.length;for(;(b=a.indexOf(c,b))>=0&&b<d;){var e=a.charCodeAt(b-1);if(e==38||e==63)if(e=a.charCodeAt(b+f),!e||e==61||e==38||e==35)return b;b+=f+1}return-1}var ob=/#|$/; function pb(a){const b=a.search(ob);let c=nb(a,0,"ase",b);if(c<0)return null;let d=a.indexOf("&",c);if(d<0||d>b)d=b;return decodeURIComponent(a.slice(c+4,d!==-1?d:0).replace(/\\+/g," "))}var qb=/[?&]($|#)/; function rb(a,b){var c=a.search(ob),d=0,f;const e=[];for(;(f=nb(a,d,"nis",c))>=0;)e.push(a.substring(d,f)),d=Math.min(a.indexOf("&",f)+1||c,c);e.push(a.slice(d));a=e.join("").replace(qb,"$1");(b="nis"+(b!=null?"="+encodeURIComponent(String(b)):""))?(c=a.indexOf("#"),c<0&&(c=a.length),d=a.indexOf("?"),d<0||d>c?(d=c,f=""):f=a.substring(d+1,c),a=[a.slice(0,d),f,a.slice(c)],c=a[1],a[1]=b?c?c+"&"+b:b:c,b=a[0]+(a[1]?"?"+a[1]:"")+a[2]):b=a;return b};function sb(a,b,c,d){let f,e;try{a.g&&a.g.g?(e=a.g.start(b.toString(),3),f=c(),a.g.end(e)):f=c()}catch(g){c=!0;try{gb(e),c=a.D(b,new Ya(g,{message:Za(g)}),void 0,d)}catch(k){a.m(217,k)}if(c)window.console?.error?.(g);else throw g;}return f}function tb(a,b,c,d){var f=X;return(...e)=>sb(f,a,()=>b.apply(c,e),d)} var vb=class{constructor(a=null){this.C=Y;this.g=a;this.i=null;this.l=!1;this.D=this.m}m(a,b,c,d,f){f=f||"jserror";let e=void 0;try{const D=new lb;var g=D;g.g.push(1);g.i[1]=W("context",a);b.error&&b.meta&&b.id||(b=new Ya(b,{message:Za(b)}));g=b;if(g.msg){b=D;var k=g.msg.substring(0,512);b.g.push(2);b.i[2]=W("msg",k)}var h=g.meta||{};k=h;if(this.i)try{this.i(k)}catch(B){}if(d)try{d(k)}catch(B){}d=D;h=[h];d.g.push(3);d.i[3]=h;var m;if(!(m=q)){d=r;h=[];let B;k=null;do{var l=d;try{var n;if(n=!!l&&l.location.href!= null)b:{try{v(l.foo);n=!0;break b}catch(z){}n=!1}var p=n}catch{p=!1}p?(B=l.location.href,k=l.document&&l.document.referrer||null):(B=k,k=null);h.push(new bb(B||""));try{d=l.parent}catch(z){d=null}}while(d&&l!==d);for(let z=0,La=h.length-1;z<=La;++z)h[z].depth=La-z;l=r;if(l.location&&l.location.ancestorOrigins&&l.location.ancestorOrigins.length===h.length-1)for(p=1;p<h.length;++p){const z=h[p];z.url||(z.url=l.location.ancestorOrigins[p-1]||"",z.g=!0)}m=h}var q=m;let R=new bb(r.location.href,!1);m= null;const la=q.length-1;for(l=la;l>=0;--l){var t=q[l];!m&&$a.test(t.url)&&(m=t);if(t.url&&!t.g){R=t;break}}t=null;const yb=q.length&&q[la].url;R.depth!==0&&yb&&(t=q[la]);e=new ab(R,t);if(e.i){q=D;var u=e.i.url||"";q.g.push(4);q.i[4]=W("top",u)}var E={url:e.g.url||""};if(e.g.url){const B=e.g.url.match(mb);var A=B[1],Ma=B[3],Na=B[4];u="";A&&(u+=A+":");Ma&&(u+="//",u+=Ma,Na&&(u+=":"+Na));var Oa=u}else Oa="";A=D;E=[E,{url:Oa}];A.g.push(5);A.i[5]=E;ub(this.C,f,D,this.l,c)}catch(D){try{ub(this.C,f,{context:"ecmserr", rctx:a,msg:Za(D),url:e?.g.url??""},this.l,c)}catch(R){}}return!0}};class wb{};function ub(a,b,c,d=!1,f,e){if((d?a.g:Math.random())<(f||.01))try{let g;c instanceof lb?g=c:(g=new lb,Ha(c,(h,m)=>{var l=g;const n=l.m++;h=W(m,h);l.g.push(n);l.i[n]=h}));const k=kb(g,"/pagead/gen_204?id="+b+"&");k&&(typeof e!=="undefined"?Ka(r,k,e):Ka(r,k))}catch(g){}}function xb(){var a=Y,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var zb=class{constructor(){this.g=Math.random()}};let Y,X; const Z=new class{constructor(a,b){this.i=[];this.l=b||r;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.i=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=V()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new eb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;U&&V()&&U.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(db()||cb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;U&&V()&&U.mark(b);!this.g||this.i.length> 2048||this.i.push(a)}}}(1,window);function Ab(){window.google_measure_js_timing||(Z.g=!1,Z.i!==Z.l.google_js_reporting_queue&&(V()&&Array.prototype.forEach.call(Z.i,gb,void 0),Z.i.length=0))} (function(a){Y=a??new zb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());xb();X=new vb(Z);X.i=b=>{var c=Ta;c!==0&&(b.jc=String(c),c=(c=Ua(c,document.currentScript))&&c.getAttribute("data-jc-version")||"unknown",b.shv=c)};X.l=!0;window.document.readyState==="complete"?Ab():Z.g&&P(window,"load",()=>{Ab()})})();function Bb(a,b,c,d){return tb(a,b,c,d)} function Cb(a,b,c,d){var f=wb;var e="B";f.B&&f.hasOwnProperty(e)||(e=new f,f.B=e);f=[];!b.eid&&f.length&&(b.eid=f.toString());ub(Y,a,b,!0,c,d)};function Db(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b};function Eb(){const a=window;if(a.gmaSdk||a.webkit?.messageHandlers?.getGmaViewSignals)return a;try{const b=window.parent;if(b.gmaSdk||b.webkit?.messageHandlers?.getGmaViewSignals)return b}catch(b){}return null} function Fb(a,b={},c=()=>{},d=()=>{},f=200,e,g){const k=String(Math.floor(Ga()*2147483647));let h=0;const m=l=>{try{const n=typeof l.data==="object"?l.data:JSON.parse(l.data);k===n.paw_id&&(window.clearTimeout(h),window.removeEventListener("message",m),n.signal?c(n.signal):n.error&&d(n.error))}catch(n){g("paw_sigs",{msg:"postmessageError",err:n instanceof Error?n.message:"nonError",data:l.data==null?"null":l.data.length>500?l.data.substring(0,500):l.data})}};window.addEventListener("message",l=>{e(903, ()=>{m(l)})()});a.postMessage({paw_id:k,...b});h=window.setTimeout(()=>{window.removeEventListener("message",m);d("PAW GMA postmessage timed out.")},f)};function Gb(a=document){return!!a.featurePolicy?.allowedFeatures().includes("attribution-reporting")};var Hb=class extends M{};function Ib(a,b){return L(a,2,b)}function Jb(a,b){return L(a,3,b)}function Kb(a,b){return L(a,4,b)}function Lb(a,b){return L(a,5,b)}function Mb(a,b){return L(a,9,b)} function Nb(a,b){{var c=b;ta(a);const l=a.j;b=l[F]|0;if(c==null)va(l,b,10);else{var d=c===fa?7:c[F]|0,f=d,e=!!(2&d)&&!!(4&d)||!!(256&d),g=e||Object.isFrozen(c),k=!0,h=!0;for(let n=0;n<c.length;n++){var m=c[n];e||(m=H(m),k&&(k=!m),h&&(h=m))}e||(d=k?13:5,d=h?d&-4097:d|4096);g&&d===f||(c=[...c],f=0,d=2&b?d|2:d&-3,d&=-273);d!==f&&(c[F]=d);b=va(l,b,10,c);2&d||!(4096&d||16&d)||(c=l,b===void 0&&(b=c[F]|0),b&32&&!(b&4096)&&(c[F]=b|4096))}}return a}function Ob(a,b){return ua(a,11,b==null?b:ka(b))} function Pb(a,b){return L(a,1,b)}function Qb(a,b){return ua(a,7,b==null?b:ka(b))}var Rb=class extends M{};const Sb="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Tb(){var a=window;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Sb).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} function Ub(a){return Ob(Nb(Lb(Ib(Pb(Kb(Qb(Mb(Jb(new Rb,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new Hb;c=L(c,1,b.brand);return L(c,2,b.version)})||[]),a.wow64||!1)}function Vb(){return Tb()?.then(a=>Ub(a))??null};class Wb{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};window.viewReq=[];function Xb(a,b){b?(b=Ia(),b.src=a.replace("&amp;","&"),b.attributionSrc="",window.viewReq.push(b)):(b=new Image,b.src=a.replace("&amp;","&"),window.viewReq.push(b))} function Yb(a,b){const c={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};b&&(c.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?c.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:c.headers={"Attribution-Reporting-Eligible":"event-source"});fetch(a,c).catch(()=>{Xb(a,b)})}function Zb(a,b){if(Q(S(),Ea)){const c=S();Ra(new Sa({v:a,u:b,o:Q(c,Da),A:Q(c,Ea)}))}else window.fetch?Yb(a,b):Xb(a,b)} function $b(){const a=r.document;return new Promise(b=>{const c=Db(a);if(c){var d=()=>{(a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]??0)!==3&&(Ja(a,c,d),b())};P(a,c,d)}})}Ta=42; window.vu=a=>{var b=Q(S(),ya)||Q(S(),Aa);const c=Eb();if(b&&c?.gmaSdk?.getViewSignals){var d=c.gmaSdk.getViewSignals();d&&!Q(S(),Aa)&&(a=O(a,"&ms="+d))}Q(S(),xa)&&"__google_lidar_radf_"in window&&(a=O(a,"&avradf=1"));const f=[];d=()=>{const k=new Wb;f.push(k.promise);return k.resolve};if(Q(S(),Ca)){var e=$b();if(e!=null){const k=d();e.then(()=>{a=O(a,"&sbtr=1");k()})}}Q(S(),Ba)&&(a=O(a,"&sbtr=1"));if(Q(S(),za)&&(e=Vb(),e!=null)){const k=d();e.then(h=>{var m=JSON.stringify(K(h));h=[];var l=0;for(var n= 0;n<m.length;n++){var p=m.charCodeAt(n);p>255&&(h[l++]=p&255,p>>=8);h[l++]=p}m=3;m===void 0&&(m=0);if(!w)for(w={},l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),n=["+/=","+/","-_=","-_.","-_"],p=0;p<5;p++){var q=l.concat(n[p].split(""));da[p]=q;for(var t=0;t<q.length;t++){var u=q[t];w[u]===void 0&&(w[u]=t)}}m=da[m];l=Array(Math.floor(h.length/3));n=m[64]||"";for(p=q=0;q<h.length-2;q+=3){var E=h[q],A=h[q+1];u=h[q+2];t=m[E>>2];E=m[(E&3)<<4|A>>4];A=m[(A&15)<<2|u>>6];u=m[u& 63];l[p++]=t+E+A+u}t=0;u=n;switch(h.length-q){case 2:t=h[q+1],u=m[(t&15)<<2]||n;case 1:h=h[q],l[p]=m[h>>2]+m[(h&3)<<4|t>>4]+u+n}h=l.join("");h.length>0&&(a=O(a,"&uach="+h));k()})}if(b&&c?.webkit?.messageHandlers?.getGmaViewSignals){const k=d();Fb(c.webkit.messageHandlers.getGmaViewSignals,{},h=>{Q(S(),Aa)||(a=O(a,"&"+h));k()},()=>{k()},200,Bb,Cb)}const g=pb(a)===(2).toString()||Fa.test(a);g&&(b=Gb(window.document)?6:5,a=rb(a,b));f.length>0?Promise.all(f).then(()=>{Zb(a,g)}):Zb(a,g)};}).call(this);</script><script>vu("https://securepubads.g.doubleclick.net/pcs/view?xai\\x3dAKAOjst2K2zfVWLqs6HCRA8TzQqr9WV0vofTCyBC17ZbGkAJYF5xuTQ7W0Eoo10dhucEs9N_2nfcuRb1tRBeXlGC3TGBehnGUxVOVGTWqObVNvexn0mWF6NY4UFM9cGs3Pw3JR4LldJ-Nyiemjn6T_wBt2vSviJOahHe1JS_VYtbfYgpx6jcodPXQ2ow1IfhN_yEf7MjYrBvKfP4wM6ybYc_8AXd0lL77np1-Vnmdq7lIPKqhe_lNA4XmcA-2RxcJsSfErSZcZNasVUfhVWeDKOzB08Te-8xfftSq1yle1-Q2Ce-qBddOJQ6qOTgwEkWkS7jyQgMx2YeeyrOjSS9XdsSl-_2zpCgL84K3GJgI_c88Ozx7j68B78Gxx2z4N7bP6egSy_rLFn_hjIxsYfvfLhRyGmPgkN8-xzAsCSh9DliBxYnlZ6GdmgaMHWZlWjN\\x26sai\\x3dAMfl-YQRyeJxHkgA0xrQsVmOc0OR4KuOxjCoqrWzSNd0ONF9m4OiR8QhOGEr0edN8R0T0Tld05SjIbBKlEfp55DMPfIrk4FFblxapvsTZmpQ0T1eMtJ7B8CdBRZ8gKevRjBY0QaEBr2CDyfTmfpbWgArHzCccWLOSjBin2fZy9Zuh3dF_TrA6Gxg8vXYn9aRKpMNsz6o1nNIkxqSgp0Q_YmQCgp1HDqM9rS84ANmBft3f1C8kV-zj9Ch7Idmh5_L50ORKig\\x26sig\\x3dCg0ArKJSzIxbYwH3RGUYEAE\\x26uach_m\\x3d%5BUACH%5D\\x26urlfix\\x3d1\\x26adurl\\x3d")</script><div class="GoogleActiveViewInnerContainer"id="avic_CIDAtoe6_o4DFQ88RAgdQD0jwQ"style="left:0px;top:0px;width:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;"></div><div style="display:inline"class="GoogleActiveViewElement"data-google-av-cxn="https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjstRbD54npmX71L5TNo577Kpi1S5ezGb6J3LgS4JAjFuUaAJT23ZNS6Mm9W6q6xh24Sl7wrAfGuj0-hkw9blMeE9sKWaC16RQ352FnEFQjEzM6EFQ-S41YTV3dujD1qEKqi6NAwDTGdNik2zM-lH1sZu0iBmXtcYT2F6Kt1Ehjj8QFZ55Ic&amp;sig=Cg0ArKJSzIlMUkqF9xO9EAE"data-google-av-adk="3326724443"data-google-av-metadata="la=0&amp;xdi=0&amp;"data-google-av-ufs-integrator-metadata="Co8BCkNtb2RlbF9wZXJzb25fY291bnRyeV9jb2RlX01YX3BlcnNvbl9yZWdpb25fY29kZV80ZDU4MmQ1MjQ1NTM1NC5qc29uEhpDSURBdG9lNl9vNERGUTg4UkFnZFFEMGp3URgBIhEItBMgtBMoAjACOAFdzcxMPii70dS5-_____8BMLvR1LkDOANAAUgAUAESmQIKjAJodHRwczovL3BhZ2VhZDIuZ29vZ2xlc3luZGljYXRpb24uY29tL3Bjcy9hY3RpdmV2aWV3P3hhaT1BS0FPanN0UmJENTRucG1YNzFMNVRObzU3N0twaTFTNWV6R2I2SjNMZ1M0SkFqRnVVYUFKVDIzWk5TNk1tOVc2cTZ4aDI0U2w3d3JBZkd1ajAtaGt3OWJsTWVFOXNLV2FDMTZSUTM1MkZuRUZRakV6TTZFRlEtUzQxWVRWM2R1akQxcUVLcWk2TkF3RFRHZE5pazJ6TS1sSDFzWnUwaUJtWHRjWVQyRjZLdDFFaGpqOFFGWjU1SWMmc2lnPUNnMEFyS0pTeklsTVVrcUY5eE85RUFFEgAaACABKAAwBBoeChpDSURBdG9lNl9vNERGUTg4UkFnZFFEMGp3URAF"data-google-av-override="-1"data-google-av-dm="2"data-google-av-aid="0"data-google-av-naid="1"data-google-av-slift=""data-google-av-cpmav=""data-google-av-btr="https://securepubads.g.doubleclick.net/pcs/view?xai=AKAOjst53cTEw1grfNwuCyuC-sxEtZLQG74D_OplxpGZK9QJGd1Ec-lWMsMSmRT2CdLlsxMeEdSxRHfHF4ozFwQIFukwUt4SIfrHri5YEef6SLXsC-DuzRbUGstHkuobvuol6lg9RmGGodPebQZ4Ise_t8ggEWmGG_GxAsxilVi0thDOANFsk3GOrpypuGZbpCr-9SkMEscBU5n_2s-76HBPrzKb02uOu266aXs5g4Vg_UVSqaHvzd7YttTV0aPResiV3G4-KHwvDbBpiMxCADes1MowJoapFFizBzDN-8xcsmO5JcojkaTQpmLegG6b12NO3k8_I8z8pz6J2QvjmMjf-Me2vzkNiTKgwlxE3DDjXFjr5w2W8eST1X_UeQBSOTtr4TgcdbBugiD2gYWELejIpYQrjwH7wGbBlBW80tNYD0I_rMDMJC_FFwJakpi0Xiw&amp;sai=AMfl-YRiZdAACaYP3Km_R4EaR8cXyCOq6ZCJ2MeGrkOTerG3vgxYjdEw-xRIZIrS_3EFv8RhdcfpODAmar48LUfk8ovDI-uC9CSpbLUCTO3VxwinYOjGLrzNtAP-1_ae1Uh8FybOPqmhkfwW0sUeAcpI-LEge-2sfeWCHvOTxcuWzYU4L9ipv7AOCftzRqRqpOfxj4mqtKPHc64Xnh_0TGSQ57kEzlhyqW9DToYKg4ZBl0v39-fcufZbd1hrWebP7gkPCuI&amp;sig=Cg0ArKJSzD38XKdUuRnDEAE&amp;uach_m=%5BUACH%5D&amp;urlfix=1&amp;adurl="data-google-av-itpl="19"data-google-av-rs="4"data-google-av-flags="[&quot;x%278440&#39;9efotm(&amp;753374%2bejvf/%27844&gt;&#39;9wuvb$&amp;56533&gt;!=|vqc)!273794&amp;&lt;qqvb/%&lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;&lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&#39;76463;21$?ebkpb$&amp;0366717&gt;*&gt;bgipf+!3=712363%9aihwc)!7202&lt;217&#39;9efotm(&amp;20061;48&amp;&gt;`dopb/%&lt;1707200!=8(&amp;2005575?&amp;&gt;`dopb/%&lt;170642?!=|vqc)!7201;=50&#39;9wuvb$&amp;03641654*&gt;bgipf+!3=731103%9aihwc)!7200?073&#39;9efotm(&amp;2004?51;&amp;&gt;`dopb/%&lt;17&gt;474&gt;!=nehu`/!36406412!9abk{a($167745;=&amp;&lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&gt;7&amp;&lt;qqvb/%&lt;104=460!=nehu`/!363;42&gt;7!9abk{a($1656;3?&lt;&amp;&lt;cbotf+*01011776%2bejvf/%72&gt;17266!=efdwa*&#39;7616?=&lt;=$?ebkpb$&amp;0335225&gt;*&gt;bgipf+!3=340764%94&gt;44653~&quot;]"><script>var amzn_win=window,amzn_c=5,amzn_x=0;while(amzn_x<amzn_c){amzn_win=amzn_win.parent;if(amzn_win.apstag)try{amzn_win.apstag.renderImp(document,"JLe4VJ9CdvUiUPCuKDiuguQAAAGYkEnuZAYAAAJYAQBhcHNfdHhuX2JpZDEgICBhcHNfdHhuX2ltcDEgICB3xo8J");amzn_x=amzn_c}catch(e){}amzn_x++};</script></div><script id="googleActiveViewDisplayScript" src="https://pagead2.googlesyndication.com/pagead/managed/js/activeview/current/ufs_web_display.js"></script><script type="text/javascript">osdlfm();</script><div style="bottom:0;right:0;width:100px;height:100px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAACASURBVBjTbZEBDsAwCALpD/j/a7cqKEtmk3ZGZFcL/Mfp6I/NwKrWTk5WClw16lT2Vliqe27meok/ant3j7z1b3eYZOKaBnfDN4zycYV8i4Q0gdwBdwn1askhW4aA+oBgb5rcJ9jmXhb32ilIPdw9s0BhzOzEO6U3c5pD4qcp8QMe5AVxdbL5ygAAAABJRU5ErkJggg==') !important;"></div><script data-jc="103" data-jc-version="r20250807" data-jcp-base_url="https://googleads.g.doubleclick.net/pagead/conversion/?ai=&amp;sigh=BpnfxIaauQU" data-jcp-cpu_label="heavy_ad_intervention_cpu" data-jcp-net_label="heavy_ad_intervention_network">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var h=this||self;function n(a,b){a:{var e=["CLOSURE_FLAGS"];for(var c=h,d=0;d<e.length;d++)if(c=c[e[d]],c==null){e=null;break a}e=c}a=e&&e[a];return a!=null?a:b};function p(a){h.setTimeout(()=>{throw a;},0)};var u=n(748402147,n(1,!0));let v=void 0;function w(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var x=w(),y=w("m_m",!0);const z=w("jas",!0);var A={};function B(a,b){return b===void 0?a.h!==C&&!!(2&(a.g[z]|0)):!!(2&b)&&a.h!==C}const C={};const D=BigInt(Number.MIN_SAFE_INTEGER),E=BigInt(Number.MAX_SAFE_INTEGER);function F(a){return a};function G(a,b,e,c){var d=c!==void 0;c=!!c;const l=[];var f=a.length;let g,k=**********,J=!1;const r=!!(b&64),q=r?b&128?0:-1:void 0;b&1||(g=f&&a[f-1],g!=null&&typeof g==="object"&&g.constructor===Object?(f--,k=f):g=void 0,!r||b&128||d||(J=!0,k=(H??F)(k-q,q,a,g,void 0)+q));b=void 0;for(d=0;d<f;d++){let m=a[d];if(m!=null&&(m=e(m,c))!=null)if(r&&d>=k){const t=d-q;(b??(b={}))[t]=m}else l[d]=m}if(g)for(let m in g){a=g[m];if(a==null||(a=e(a,c))==null)continue;f=+m;let t;r&&!Number.isNaN(f)&&(t=f+q)<k?l[t]= a:(b??(b={}))[m]=a}b&&(J?l.push(b):l[k]=b);return l}function I(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=D&&a<=E?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[z]|0;return a.length===0&&b&1?void 0:G(a,b,I)}if(a!=null&&a[y]===A)return K(a);return}return a}let H;function K(a){a=a.g;return G(a,a[z]|0,I)};function L(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[z]|0;if(u&&1&b)throw Error("rfarr");2048&b&&!(2&b)&&M();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[z]=b|2048),a;var e=a;b|=64;var c=e.length;if(c){var d=c-1;c=e[d];if(c!=null&&typeof c==="object"&&c.constructor===Object){const l=b&128?0:-1;d-=l;if(d>=1024)throw Error("pvtlmt");for(const f in c){const g=+f;if(g<d)e[g+l]=c[f],delete c[f];else break}b=b&-8380417|(d&1023)<<13}}}a[z]=b|2112;return a} function M(){if(u)throw Error("carr");if(x!=null){var a=v??(v={});var b=a[x]||0;b>=5||(a[x]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",p(a))}};function N(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var e=a[z]|0;a.length===0&&e&1?a=void 0:e&2||(!b||4096&e||16&e?a=O(a,e,!1,b&&!(e&16)):(a[z]|=34,e&4&&Object.freeze(a)));return a}if(a!=null&&a[y]===A){e=a.g;const c=e[z]|0;B(a,c)||(c&2?b=!0:c&32&&!(c&4096)?(e[z]=c|2,a.h=C,b=!0):b=!1,b?(a=new a.constructor(e),a.m=C):a=O(e,c));return a}}function O(a,b,e,c){c??(c=!!(34&b));a=G(a,b,N,c);c=32;e&&(c|=2);b=b&8380609|c;a[z]=b;return a};function P(a,b,e){if(e!=null&&typeof e!=="string")throw Error();if(a.h===C){var c=a.g;c=O(c,c[z]|0);c[z]|=2048;a.g=c;a.h=void 0;a.m=void 0;c=!0}else c=!1;if(!c&&B(a,a.g[z]|0))throw Error();a=a.g;a:{var d=a[z]|0;c=b+-1;const l=a.length-1;if(l>=0&&c>=l){const f=a[l];if(f!=null&&typeof f==="object"&&f.constructor===Object){f[b]=e;break a}}c<=l?a[c]=e:e!==void 0&&(d=(d??a[z]|0)>>13&1023||536870912,b>=d?e!=null&&(a[d+-1]={[b]:e}):a[c]=e)}};var Q=class{constructor(a){this.g=L(a)}toJSON(){return K(this)}};Q.prototype[y]=A;Q.prototype.toString=function(){return this.g.toString()};var R=class extends Q{};function S(a=window){return a};var T=/#|$/;const U=function(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)}(103,document.currentScript);if(U==null)throw Error("JSC not found 103");const V={},W=U.attributes;for(let a=W.length-1;a>=0;a--){const b=W[a].name;b.indexOf("data-jcp-")===0&&(V[b.substring(9)]=W[a].value)} (function(a,b,e){var c=window;a&&b&&e&&c.ReportingObserver&&c.fetch&&(new c.ReportingObserver((d,l)=>{d=d[0];if(d?.body?.id==="HeavyAdIntervention"){d=(d.body.message?.indexOf("network")||0)>0?e:b;var f=a.search(T);var g;b:{for(g=0;(g=a.indexOf("ad_signals",g))>=0&&g<f;){var k=a.charCodeAt(g-1);if(k==38||k==63)if(k=a.charCodeAt(g+10),!k||k==61||k==38||k==35)break b;g+=11}g=-1}k=g;if(k<0)f=null;else{g=a.indexOf("&",k);if(g<0||g>f)g=f;f=decodeURIComponent(a.slice(k+11,g!==-1?g:0).replace(/\\+/g," "))}f? (navigator.sendBeacon("https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&label="+d),d={i:f,label:d},f=new R,d!=null&&(d.i!=null&&P(f,1,d.i),d.u!=null&&P(f,3,d.u),d.label!=null&&P(f,6,d.label),d.l!=null&&P(f,7,d.l),d.j!=null&&P(f,8,d.j),d.o!=null&&P(f,11,d.o)),S(h).fence?.reportEvent({eventType:"interaction",eventData:JSON.stringify(K(f)),destination:["buyer"]})):c.fetch(`${a}&label=${d}`,{keepalive:!0,method:"get",mode:"no-cors"});l.disconnect()}},{types:["intervention"], buffered:!0})).observe()})(V.base_url,V.cpu_label,V.net_label);}).call(this);</script></body></html>
