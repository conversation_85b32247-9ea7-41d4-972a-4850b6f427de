{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "broken_count": 1, "host": "cm.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 1, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "whoer.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "pagead2.googlesyndication.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 2, "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "googleads.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 4, "host": "pagead2.googlesyndication.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "cm.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "x.bidswitch.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 3, "host": "fundingchoicesmessages.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 2, "host": "tpc.googlesyndication.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "a.sportradarserving.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "www.spotify.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 2, "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "www.expedia.de", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "ep2.adtrafficquality.google", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "ep1.adtrafficquality.google", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "analytics.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "api-main.whox.is", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "broken_count": 1, "host": "www.googleadservices.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "medium.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "www.airbnb.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "www.facebook.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "www.tumblr.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "www.amazon.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "zh.airbnb.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "accounts.spotify.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 1, "host": "beacons.gcp.gvt2.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "broken_count": 1, "host": "beacons.gcp.gvt2.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "broken_count": 1, "host": "content-autofill.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", true, 0], "broken_count": 1, "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 2, "host": "beacons.gcp.gvt2.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 2, "host": "ogads-pa.clients6.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "broken_count": 2, "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 13, "broken_until": "**********", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", true, 0], "broken_count": 5, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 5, "broken_until": "**********", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", true, 0], "broken_count": 5, "broken_until": "**********", "host": "play.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 4, "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "broken_count": 3, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "broken_count": 6, "broken_until": "**********", "host": "android.clients.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://ads-api.x.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://cm.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://analytics.twitter.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399327669490145", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833272685017", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://static.cloudflareinsights.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833273992084", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.google.co.kr", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833276486622", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://um.simpli.fi", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399327677706198", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://www.temu.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://analytics.pangle-ads.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://creativecdn.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://sync-tm.everesttech.net", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://tracking.prismpartner.smt.docomo.ne.jp", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://pr-bh.ybp.yahoo.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://tpc.googlesyndication.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399327678856801", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://px.ads.linkedin.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://a.sportradarserving.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://x.bidswitch.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833283736566", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://ep2.adtrafficquality.google", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://code.jquery.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13399327668902112", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://stackpath.bootstrapcdn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833269078698", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.googleoptimize.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://cdn.botfaqtor.ru", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://api-main.whox.is", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://rt.gsspat.jp", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://www.googleadservices.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://dsp.360yield.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833277428397", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://dis.criteo.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://api.ipify.org", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833307369005", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://ipac.ctnsnet.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833307507531", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://tr-us.adsmoloco.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://cms.quantserve.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://twitter.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://de.foursquare.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://github.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://squareup.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.paypal.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://secure.meetup.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://slack.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.tumblr.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.academia.edu", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.facebook.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://500px.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://medium.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.airbnb.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://ep2.adtrafficquality.google", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://googleads.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.amazon.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.meetup.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.reddit.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://x.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://vk.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://pagead2.googlesyndication.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://app.squareup.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.spotify.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://www.expedia.de", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://zh.airbnb.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://fundingchoicesmessages.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://ep1.adtrafficquality.google", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://accounts.spotify.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://cm.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://mc.yandex.ru", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://mc.yandex.ru", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://whoer.net", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://pagead2.googlesyndication.com", "supports_spdy": true}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://e2c61.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833337605701", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", true, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833338611471", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://beacons.gvt2.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL3dob2VyLm5ldAAAAA==", false, 0], "server": "https://e2c65.gcp.gvt2.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://twitter.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401878885053088", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://csp.withgoogle.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833249964534", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401833248898262", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://upload.x.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13401891879233162", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://beacons.gcp.gvt2.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", true, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", true, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", true, 0], "server": "https://play.google.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://abs.twimg.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://android.clients.google.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://abs-0.twimg.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://pbs.twimg.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://video.twimg.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://api.x.com", "supports_spdy": true}, {"anonymization": ["FAAAAA0AAABodHRwczovL3guY29tAAAA", false, 0], "server": "https://x.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "3G"}}}