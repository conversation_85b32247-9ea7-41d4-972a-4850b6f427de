(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.Witstroom=f()}})(function(){var define,module,exports;return(function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){"use strict";var __extends=(this&&this.__extends)||(function(){var extendStatics=Object.setPrototypeOf||({__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b;})||function(d,b){for(var p in b)if(b.hasOwnProperty(p))d[p]=b[p];};return function(d,b){extendStatics(d,b);function __(){this.constructor=d;}
d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __());};})();var __awaiter=(this&&this.__awaiter)||function(thisArg,_arguments,P,generator){return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value));}catch(e){reject(e);}}
function rejected(value){try{step(generator["throw"](value));}catch(e){reject(e);}}
function step(result){result.done?resolve(result.value):new P(function(resolve){resolve(result.value);}).then(fulfilled,rejected);}
step((generator=generator.apply(thisArg,_arguments||[])).next());});};var __generator=(this&&this.__generator)||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1];},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),"throw":verb(1),"return":verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this;}),g;function verb(n){return function(v){return step([n,v]);};}
function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=y[op[0]&2?"return":op[0]?"throw":"next"])&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[0,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue;}
if(op[0]===3&&(!t||(op[1]>t[0]&&op[1]<t[3]))){_.label=op[1];break;}
if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break;}
if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break;}
if(t[2])_.ops.pop();_.trys.pop();continue;}
op=body.call(thisArg,_);}catch(e){op=[6,e];y=0;}finally{f=t=0;}
if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true};}};Object.defineProperty(exports,"__esModule",{value:true});var http_1=require("./http");var events_1=require("events");var Client=(function(_super){__extends(Client,_super);function Client(uri,id){var _this=_super.call(this)||this;_this._initialized=false;_this._id=id;_this._uri=uri+"/visit";return _this;}
Client.prototype.on=function(ev,callback){_super.prototype.on.call(this,ev,callback);};Client.prototype.check=function(href,i,u,f){return __awaiter(this,void 0,void 0,function(){var elements,result;return __generator(this,function(_a){switch(_a.label){case 0:elements={i:i,u:u,f:f};return[4,http_1.Http.post_statusData(href,elements)];case 1:result=_a.sent();return[2,result];}});});};return Client;}(events_1.EventEmitter));exports.Client=Client;},{"./http":6,"events":13}],2:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var EverCookieOptions=(function(){function EverCookieOptions(){var host=window.location.host;if(host.indexOf("www.")>=0){host=host.split("www.")[1];}
this.domain=host.replace(/:\d+/,"");}
return EverCookieOptions;}());exports.EverCookieOptions=EverCookieOptions;},{}],3:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var Cookie=(function(){function Cookie(options){this._options=options;}
Cookie.prototype.set=function(key,value){this.cookie(key,value);};Cookie.prototype.get=function(key){return Promise.resolve(this.cookie(key));};Cookie.prototype.delete=function(key){document.cookie=key+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC;";};Cookie.prototype.cookie=function(key,value){if(value===void 0){value=undefined;}
if(value!==undefined){document.cookie=key+"=; expires=Mon, 20 Sep 2010 00:00:00 UTC; path=/; domain="+
this._options.domain;document.cookie=key+"="+
value+"; expires=Tue, 31 Dec 2099 00:00:00 UTC; path=/; domain="+
this._options.domain;return value;}
else{return this.getFromStr(key,document.cookie);}};Cookie.prototype.getFromStr=function(name,text){if(typeof text!=="string"){return;}
var nameEQ=name+"=",ca=text.split(/[;&]/),i,c;for(i=0;i<ca.length;i++){c=ca[i];while(c.charAt(0)===" "){c=c.substring(1,c.length);}
if(c.indexOf(nameEQ)===0){return c.substring(nameEQ.length,c.length);}}};return Cookie;}());exports.Cookie=Cookie;},{}],4:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var fingerprintData_1=require("./fingerprintData");var ua_parser_js_1=require("ua-parser-js");var fp_new=new fingerprintData_1.Fingerprint2();var Fingerprint=(function(){function Fingerprint(value,components){this.value=value;this.components=components;}
Fingerprint.prototype.getValue=function(){return this.value;};Fingerprint.prototype.getComponents=function(){return this.components;};Fingerprint.get=function(){function joinFingerprintObjects(array,sep){var res="";for(var i=0;i<array.length;i++){var obj=array[i];if(res!="")
res+=sep;res+=obj.key+","+obj.value;}
return res;}
function getFonts(){var baseFonts=["monospace","sans-serif","serif"];var fontList=["Andale Mono","Arial","Arial Black","Arial Hebrew","Arial MT","Arial Narrow","Arial Rounded MT Bold","Arial Unicode MS","Bitstream Vera Sans Mono","Book Antiqua","Bookman Old Style","Calibri","Cambria","Cambria Math","Century","Century Gothic","Century Schoolbook","Comic Sans","Comic Sans MS","Consolas","Courier","Courier New","Garamond","Geneva","Georgia","Helvetica","Helvetica Neue","Impact","Lucida Bright","Lucida Calligraphy","Lucida Console","Lucida Fax","LUCIDA GRANDE","Lucida Handwriting","Lucida Sans","Lucida Sans Typewriter","Lucida Sans Unicode","Microsoft Sans Serif","Monaco","Monotype Corsiva","MS Gothic","MS Outlook","MS PGothic","MS Reference Sans Serif","MS Sans Serif","MS Serif","MYRIAD","MYRIAD PRO","Palatino","Palatino Linotype","Segoe Print","Segoe Script","Segoe UI","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Tahoma","Times","Times New Roman","Times New Roman PS","Trebuchet MS","Verdana","Wingdings","Wingdings 2","Wingdings 3",];fontList=fontList.filter(function(font,position){return fontList.indexOf(font)===position;});var testString="mmmmmmmmmmlli";var testSize="72px";var h=document.getElementsByTagName("html")[0];var baseFontsDiv=document.createElement("div");var fontsDiv=document.createElement("div");var defaultWidth={};var defaultHeight={};var createSpan=function(){var s=document.createElement("span");s.style.position="absolute";s.style.left="-9999px";s.style.fontSize=testSize;s.style.lineHeight="normal";s.innerHTML=testString;return s;};var createSpanWithFonts=function(fontToDetect,baseFont){var s=createSpan();s.style.fontFamily="'"+fontToDetect+"',"+baseFont;return s;};var initializeBaseFontsSpans=function(){var spans=[];for(var index=0,length=baseFonts.length;index<length;index++){var s=createSpan();s.style.fontFamily=baseFonts[index];baseFontsDiv.appendChild(s);spans.push(s);}
return spans;};var initializeFontsSpans=function(){var spans={};for(var i=0,l=fontList.length;i<l;i++){var fontSpans=[];for(var j=0,numDefaultFonts=baseFonts.length;j<numDefaultFonts;j++){var s=createSpanWithFonts(fontList[i],baseFonts[j]);fontsDiv.appendChild(s);fontSpans.push(s);}
spans[fontList[i]]=fontSpans;}
return spans;};var isFontAvailable=function(fontSpans){var detected=false;for(var i=0;i<baseFonts.length;i++){detected=fontSpans[i].offsetWidth!==defaultWidth[baseFonts[i]]||fontSpans[i].offsetHeight!==defaultHeight[baseFonts[i]];if(detected){return detected;}}
return detected;};var baseFontsSpans=initializeBaseFontsSpans();h.appendChild(baseFontsDiv);for(var index=0,length=baseFonts.length;index<length;index++){defaultWidth[baseFonts[index]]=baseFontsSpans[index].offsetWidth;defaultHeight[baseFonts[index]]=baseFontsSpans[index].offsetHeight;}
var fontsSpans=initializeFontsSpans();h.appendChild(fontsDiv);var available=[];for(var i=0,l=fontList.length;i<l;i++){if(isFontAvailable(fontsSpans[fontList[i]])){available.push(fontList[i]);}}
h.removeChild(fontsDiv);h.removeChild(baseFontsDiv);return available;}
function getWebglCanvas(){var canvas=document.createElement("canvas");var gl=null;try{gl=canvas.getContext("webgl")||canvas.getContext("experimental-webgl");}
catch(e){}
if(!gl){gl=null;}
return gl;}
function getWebglVendorAndRenderer(){try{var glContext=getWebglCanvas();var extensionDebugRendererInfo=glContext.getExtension("WEBGL_debug_renderer_info");return(glContext.getParameter(extensionDebugRendererInfo.UNMASKED_VENDOR_WEBGL)+"~"+
glContext.getParameter(extensionDebugRendererInfo.UNMASKED_RENDERER_WEBGL));}
catch(e){return null;}}
function getBrowserName(){var userAgent=navigator.userAgent;if(userAgent.indexOf("MiuiBrowser")>=0){return"MiuiBrowser";}
else if(userAgent.indexOf("NetCast")>=0){return"NetCast";}
var parser=new ua_parser_js_1.UAParser(userAgent);var result=parser.getResult();return result.browser.name;}
var f;var nav=navigator;(function(){return fp_new.get({NOT_AVAILABLE:null,ERROR:null,excludes:{userAgent:true,timezone:true,timezoneOffset:true,adBlock:true,doNotTrack:true,hasLiedBrowser:true,hasLiedLanguages:true,hasLiedOs:true,hasLiedResolution:true,},extraComponents:[{key:"vendor",getData:function(done,options){if(nav.vendor==undefined){done("not available");}
else
done(nav.vendor);},},{key:"productSub",getData:function(done,options){var p=nav.productSub;if(p==undefined||typeof(p)!='string'){done("not available");}
else
done(p);},},{key:"pluginsBool",getData:function(done,options){done(nav.plugins!=null&&nav.plugins!=undefined);},},{key:"pluginsLenght",getData:function(done,options){try{var length_1=nav.plugins.length;if(length_1==undefined)
done(null);else
done(length_1);}
catch(_a){done(null);}},},{key:"activeXObject",getData:function(done,options){done(!!("ActiveXObject"in window));},},{key:"browserName",getData:function(done,options){try{done(getBrowserName());}
catch(_a){done("not available");}},},{key:"fonts",getData:function(done,options){try{done(getFonts());}
catch(_a){done("not available");}},},],preprocessor:function(key,value){if(key=="deviceMemory"){var m=nav.deviceMemory;if(m==undefined||typeof(m)!="number")
return null;else{return m;}}
else if(key=="webglVendorAndRenderer"){var webgl=getWebglVendorAndRenderer();if(webgl==undefined)
return"not available";else{return webgl;}}
else{return value;}},},function(c){var hash=fp_new.x64hash128(joinFingerprintObjects(c,""),31);f=new Fingerprint(hash,c);});})();return f;};return Fingerprint;}());exports.Fingerprint=Fingerprint;},{"./fingerprintData":5,"ua-parser-js":14}],5:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var nav=navigator;var wnd=window;var doc=document;'use strict';var x64Add=function(m,n){m=[m[0]>>>16,m[0]&0xffff,m[1]>>>16,m[1]&0xffff];n=[n[0]>>>16,n[0]&0xffff,n[1]>>>16,n[1]&0xffff];var o=[0,0,0,0];o[3]+=m[3]+n[3];o[2]+=o[3]>>>16;o[3]&=0xffff;o[2]+=m[2]+n[2];o[1]+=o[2]>>>16;o[2]&=0xffff;o[1]+=m[1]+n[1];o[0]+=o[1]>>>16;o[1]&=0xffff;o[0]+=m[0]+n[0];o[0]&=0xffff;return[(o[0]<<16)|o[1],(o[2]<<16)|o[3]];};var x64Multiply=function(m,n){m=[m[0]>>>16,m[0]&0xffff,m[1]>>>16,m[1]&0xffff];n=[n[0]>>>16,n[0]&0xffff,n[1]>>>16,n[1]&0xffff];var o=[0,0,0,0];o[3]+=m[3]*n[3];o[2]+=o[3]>>>16;o[3]&=0xffff;o[2]+=m[2]*n[3];o[1]+=o[2]>>>16;o[2]&=0xffff;o[2]+=m[3]*n[2];o[1]+=o[2]>>>16;o[2]&=0xffff;o[1]+=m[1]*n[3];o[0]+=o[1]>>>16;o[1]&=0xffff;o[1]+=m[2]*n[2];o[0]+=o[1]>>>16;o[1]&=0xffff;o[1]+=m[3]*n[1];o[0]+=o[1]>>>16;o[1]&=0xffff;o[0]+=(m[0]*n[3])+(m[1]*n[2])+(m[2]*n[1])+(m[3]*n[0]);o[0]&=0xffff;return[(o[0]<<16)|o[1],(o[2]<<16)|o[3]];};var x64Rotl=function(m,n){n%=64;if(n===32){return[m[1],m[0]];}
else if(n<32){return[(m[0]<<n)|(m[1]>>>(32-n)),(m[1]<<n)|(m[0]>>>(32-n))];}
else{n-=32;return[(m[1]<<n)|(m[0]>>>(32-n)),(m[0]<<n)|(m[1]>>>(32-n))];}};var x64LeftShift=function(m,n){n%=64;if(n===0){return m;}
else if(n<32){return[(m[0]<<n)|(m[1]>>>(32-n)),m[1]<<n];}
else{return[m[1]<<(n-32),0];}};var x64Xor=function(m,n){return[m[0]^n[0],m[1]^n[1]];};var x64Fmix=function(h){h=x64Xor(h,[0,h[0]>>>1]);h=x64Multiply(h,[0xff51afd7,0xed558ccd]);h=x64Xor(h,[0,h[0]>>>1]);h=x64Multiply(h,[0xc4ceb9fe,0x1a85ec53]);h=x64Xor(h,[0,h[0]>>>1]);return h;};var x64hash128=function(key,seed){key=key||'';seed=seed||0;var remainder=key.length%16;var bytes=key.length-remainder;var h1=[0,seed];var h2=[0,seed];var k1=[0,0];var k2=[0,0];var c1=[0x87c37b91,0x114253d5];var c2=[0x4cf5ad43,0x2745937f];for(var i=0;i<bytes;i=i+16){k1=[((key.charCodeAt(i+4)&0xff))|((key.charCodeAt(i+5)&0xff)<<8)|((key.charCodeAt(i+6)&0xff)<<16)|((key.charCodeAt(i+7)&0xff)<<24),((key.charCodeAt(i)&0xff))|((key.charCodeAt(i+1)&0xff)<<8)|((key.charCodeAt(i+2)&0xff)<<16)|((key.charCodeAt(i+3)&0xff)<<24)];k2=[((key.charCodeAt(i+12)&0xff))|((key.charCodeAt(i+13)&0xff)<<8)|((key.charCodeAt(i+14)&0xff)<<16)|((key.charCodeAt(i+15)&0xff)<<24),((key.charCodeAt(i+8)&0xff))|((key.charCodeAt(i+9)&0xff)<<8)|((key.charCodeAt(i+10)&0xff)<<16)|((key.charCodeAt(i+11)&0xff)<<24)];k1=x64Multiply(k1,c1);k1=x64Rotl(k1,31);k1=x64Multiply(k1,c2);h1=x64Xor(h1,k1);h1=x64Rotl(h1,27);h1=x64Add(h1,h2);h1=x64Add(x64Multiply(h1,[0,5]),[0,0x52dce729]);k2=x64Multiply(k2,c2);k2=x64Rotl(k2,33);k2=x64Multiply(k2,c1);h2=x64Xor(h2,k2);h2=x64Rotl(h2,31);h2=x64Add(h2,h1);h2=x64Add(x64Multiply(h2,[0,5]),[0,0x38495ab5]);}
k1=[0,0];k2=[0,0];switch(remainder){case 15:k2=x64Xor(k2,x64LeftShift([0,key.charCodeAt(i+14)],48));case 14:k2=x64Xor(k2,x64LeftShift([0,key.charCodeAt(i+13)],40));case 13:k2=x64Xor(k2,x64LeftShift([0,key.charCodeAt(i+12)],32));case 12:k2=x64Xor(k2,x64LeftShift([0,key.charCodeAt(i+11)],24));case 11:k2=x64Xor(k2,x64LeftShift([0,key.charCodeAt(i+10)],16));case 10:k2=x64Xor(k2,x64LeftShift([0,key.charCodeAt(i+9)],8));case 9:k2=x64Xor(k2,[0,key.charCodeAt(i+8)]);k2=x64Multiply(k2,c2);k2=x64Rotl(k2,33);k2=x64Multiply(k2,c1);h2=x64Xor(h2,k2);case 8:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+7)],56));case 7:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+6)],48));case 6:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+5)],40));case 5:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+4)],32));case 4:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+3)],24));case 3:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+2)],16));case 2:k1=x64Xor(k1,x64LeftShift([0,key.charCodeAt(i+1)],8));case 1:k1=x64Xor(k1,[0,key.charCodeAt(i)]);k1=x64Multiply(k1,c1);k1=x64Rotl(k1,31);k1=x64Multiply(k1,c2);h1=x64Xor(h1,k1);}
h1=x64Xor(h1,[0,key.length]);h2=x64Xor(h2,[0,key.length]);h1=x64Add(h1,h2);h2=x64Add(h2,h1);h1=x64Fmix(h1);h2=x64Fmix(h2);h1=x64Add(h1,h2);h2=x64Add(h2,h1);return('00000000'+(h1[0]>>>0).toString(16)).slice(-8)+('00000000'+(h1[1]>>>0).toString(16)).slice(-8)+('00000000'+(h2[0]>>>0).toString(16)).slice(-8)+('00000000'+(h2[1]>>>0).toString(16)).slice(-8);};var defaultOptions={preprocessor:null,audio:{timeout:1000,excludeIOS11:true},fonts:{swfContainerId:'fingerprintjs2',swfPath:'flash/compiled/FontList.swf',userDefinedFonts:[],extendedJsFonts:false},screen:{detectScreenOrientation:false},plugins:{sortPluginsFor:[/palemoon/i],excludeIE:false},extraComponents:[],excludes:{'enumerateDevices':true,'pixelRatio':true,'doNotTrack':true,'fontsFlash':true},NOT_AVAILABLE:'not available',ERROR:'error',EXCLUDED:'excluded'};var each=function(obj,iterator){if(Array.prototype.forEach&&obj.forEach===Array.prototype.forEach){obj.forEach(iterator);}
else if(obj.length===+obj.length){for(var i=0,l=obj.length;i<l;i++){iterator(obj[i],i,obj);}}
else{for(var key in obj){if(obj.hasOwnProperty(key)){iterator(obj[key],key,obj);}}}};var map=function(obj,iterator){var results=[];if(obj==null){return results;}
if(Array.prototype.map&&obj.map===Array.prototype.map){return obj.map(iterator);}
each(obj,function(value,index,list){results.push(iterator(value,index,list));});return results;};var extendSoft=function(target,source){if(source==null){return target;}
var value;var key;for(key in source){value=source[key];if(value!=null&&!(Object.prototype.hasOwnProperty.call(target,key))){target[key]=value;}}
return target;};var isEnumerateDevicesSupported=function(){return(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices);};var UserAgent=function(done){done(navigator.userAgent);};var languageKey=function(done,options){done(navigator.language||nav.userLanguage||nav.browserLanguage||nav.systemLanguage||options.NOT_AVAILABLE);};var colorDepthKey=function(done,options){done(window.screen.colorDepth||options.NOT_AVAILABLE);};var deviceMemoryKey=function(done,options){done(nav.deviceMemory||options.NOT_AVAILABLE);};var pixelRatioKey=function(done,options){done(window.devicePixelRatio||options.NOT_AVAILABLE);};var screenResolutionKey=function(done,options){done(getScreenResolution(options));};var getScreenResolution=function(options){var resolution=[window.screen.width,window.screen.height];return resolution;};var availableScreenResolutionKey=function(done,options){done(getAvailableScreenResolution(options));};var getAvailableScreenResolution=function(options){if(window.screen.availWidth&&window.screen.availHeight){var available=[window.screen.availWidth,window.screen.availHeight];return available;}
return options.NOT_AVAILABLE;};var timezoneOffset=function(done){done(new Date().getTimezoneOffset());};var timezone=function(done,options){if(wnd.Intl&&wnd.Intl.DateTimeFormat){done(new wnd.Intl.DateTimeFormat().resolvedOptions().timeZone);return;}
done(options.NOT_AVAILABLE);};var sessionStorageKey=function(done,options){done(hasSessionStorage(options));};var localStorageKey=function(done,options){done(hasLocalStorage(options));};var indexedDbKey=function(done,options){done(hasIndexedDB(options));};var addBehaviorKey=function(done){done(!!(doc.body&&doc.body.addBehavior));};var openDatabaseKey=function(done){done(!!wnd.openDatabase);};var cpuClassKey=function(done,options){done(getNavigatorCpuClass(options));};var platformKey=function(done,options){done(getNavigatorPlatform(options));};var doNotTrackKey=function(done,options){done(getDoNotTrack(options));};var canvasKey=function(done,options){if(isCanvasSupported()){done(getCanvasFp(options));return;}
done(options.NOT_AVAILABLE);};var webglKey=function(done,options){if(isWebGlSupported()){done(getWebglFp());return;}
done(options.NOT_AVAILABLE);};var webglVendorAndRendererKey=function(done){if(isWebGlSupported()){done(getWebglVendorAndRenderer());return;}
done();};var adBlockKey=function(done){done(getAdBlock());};var hasLiedLanguagesKey=function(done){done(getHasLiedLanguages());};var hasLiedResolutionKey=function(done){done(getHasLiedResolution());};var hasLiedOsKey=function(done){done(getHasLiedOs());};var hasLiedBrowserKey=function(done){done(getHasLiedBrowser());};var flashFontsKey=function(done,options){if(!hasSwfObjectLoaded()){return done('swf object not loaded');}
if(!hasMinFlashInstalled()){return done('flash not installed');}
if(!options.fonts.swfPath){return done('missing options.fonts.swfPath');}
loadSwfAndDetectFonts(function(fonts){done(fonts);},options);};var pluginsComponent=function(done,options){if(isIE()){if(!options.plugins.excludeIE){done(getIEPlugins(options));}
else{done(options.EXCLUDED);}}
else{done(getRegularPlugins(options));}};var getRegularPlugins=function(options){if(navigator.plugins==null){return options.NOT_AVAILABLE;}
var plugins=[];for(var i=0,l=navigator.plugins.length;i<l;i++){if(navigator.plugins[i]){plugins.push(navigator.plugins[i]);}}
if(pluginsShouldBeSorted(options)){plugins=plugins.sort(function(a,b){if(a.name>b.name){return 1;}
if(a.name<b.name){return-1;}
return 0;});}
return map(plugins,function(p){var mimeTypes=map(p,function(mt){return[mt.type,mt.suffixes];});return[p.name,p.description,mimeTypes];});};var getIEPlugins=function(options){var result=[];if((Object.getOwnPropertyDescriptor&&Object.getOwnPropertyDescriptor(window,'ActiveXObject'))||('ActiveXObject'in window)){var names=['AcroPDF.PDF','Adodb.Stream','AgControl.AgControl','DevalVRXCtrl.DevalVRXCtrl.1','MacromediaFlashPaper.MacromediaFlashPaper','Msxml2.DOMDocument','Msxml2.XMLHTTP','PDF.PdfCtrl','QuickTime.QuickTime','QuickTimeCheckObject.QuickTimeCheck.1','RealPlayer','RealPlayer.RealPlayer(tm) ActiveX Control (32-bit)','RealVideo.RealVideo(tm) ActiveX Control (32-bit)','Scripting.Dictionary','SWCtl.SWCtl','Shell.UIHelper','ShockwaveFlash.ShockwaveFlash','Skype.Detection','TDCCtl.TDCCtl','WMPlayer.OCX','rmocx.RealPlayer G2 Control','rmocx.RealPlayer G2 Control.1'];result=map(names,function(name){try{new wnd.ActiveXObject(name);return name;}
catch(e){return options.ERROR;}});}
else{result.push(options.NOT_AVAILABLE);}
if(navigator.plugins){result=result.concat(getRegularPlugins(options));}
return result;};var pluginsShouldBeSorted=function(options){var should=false;for(var i=0,l=options.plugins.sortPluginsFor.length;i<l;i++){var re=options.plugins.sortPluginsFor[i];if(navigator.userAgent.match(re)){should=true;break;}}
return should;};var touchSupportKey=function(done){done(getTouchSupport());};var hardwareConcurrencyKey=function(done,options){done(getHardwareConcurrency(options));};var hasSessionStorage=function(options){try{return!!window.sessionStorage;}
catch(e){return options.ERROR;}};var hasLocalStorage=function(options){try{return!!window.localStorage;}
catch(e){return options.ERROR;}};var hasIndexedDB=function(options){try{return!!window.indexedDB;}
catch(e){return options.ERROR;}};var getHardwareConcurrency=function(options){if(navigator.hardwareConcurrency){return navigator.hardwareConcurrency;}
return options.NOT_AVAILABLE;};var getNavigatorCpuClass=function(options){return nav.cpuClass||options.NOT_AVAILABLE;};var getNavigatorPlatform=function(options){if(navigator.platform){return navigator.platform;}
else{return options.NOT_AVAILABLE;}};var getDoNotTrack=function(options){if(navigator.doNotTrack){return navigator.doNotTrack;}
else if(nav.msDoNotTrack){return nav.msDoNotTrack;}
else if(wnd.doNotTrack){return wnd.doNotTrack;}
else{return options.NOT_AVAILABLE;}};var getTouchSupport=function(){var maxTouchPoints=0;var touchEvent;if(typeof nav.maxTouchPoints!=='undefined'){maxTouchPoints=nav.maxTouchPoints;}
else if(typeof nav.msMaxTouchPoints!=='undefined'){maxTouchPoints=nav.msMaxTouchPoints;}
try{document.createEvent('TouchEvent');touchEvent=true;}
catch(_){touchEvent=false;}
var touchStart='ontouchstart'in window;return[maxTouchPoints,touchEvent,touchStart];};var getCanvasFp=function(options){var result=[];var canvas=document.createElement('canvas');canvas.width=2000;canvas.height=200;canvas.style.display='inline';var ctx=canvas.getContext('2d');ctx.rect(0,0,10,10);ctx.rect(2,2,6,6);result.push('canvas winding:'+((ctx.isPointInPath(5,5,'evenodd')===false)?'yes':'no'));ctx.textBaseline='alphabetic';ctx.fillStyle='#f60';ctx.fillRect(125,1,62,20);ctx.fillStyle='#069';if(options.dontUseFakeFontInCanvas){ctx.font='11pt Arial';}
else{ctx.font='11pt no-real-font-123';}
ctx.fillText('Cwm fjordbank glyphs vext quiz, \ud83d\ude03',2,15);ctx.fillStyle='rgba(102, 204, 0, 0.2)';ctx.font='18pt Arial';ctx.fillText('Cwm fjordbank glyphs vext quiz, \ud83d\ude03',4,45);ctx.globalCompositeOperation='multiply';ctx.fillStyle='rgb(255,0,255)';ctx.beginPath();ctx.arc(50,50,50,0,Math.PI*2,true);ctx.closePath();ctx.fill();ctx.fillStyle='rgb(0,255,255)';ctx.beginPath();ctx.arc(100,50,50,0,Math.PI*2,true);ctx.closePath();ctx.fill();ctx.fillStyle='rgb(255,255,0)';ctx.beginPath();ctx.arc(75,100,50,0,Math.PI*2,true);ctx.closePath();ctx.fill();ctx.fillStyle='rgb(255,0,255)';ctx.arc(75,75,75,0,Math.PI*2,true);ctx.arc(75,75,25,0,Math.PI*2,true);ctx.fill('evenodd');if(canvas.toDataURL){result.push('canvas fp:'+canvas.toDataURL());}
return result;};var getWebglFp=function(){var gl;var fa2s=function(fa){gl.clearColor(0.0,0.0,0.0,1.0);gl.enable(gl.DEPTH_TEST);gl.depthFunc(gl.LEQUAL);gl.clear(gl.COLOR_BUFFER_BIT|gl.DEPTH_BUFFER_BIT);return'['+fa[0]+', '+fa[1]+']';};var maxAnisotropy=function(gl){var ext=gl.getExtension('EXT_texture_filter_anisotropic')||gl.getExtension('WEBKIT_EXT_texture_filter_anisotropic')||gl.getExtension('MOZ_EXT_texture_filter_anisotropic');if(ext){var anisotropy=gl.getParameter(ext.MAX_TEXTURE_MAX_ANISOTROPY_EXT);if(anisotropy===0){anisotropy=2;}
return anisotropy;}
else{return null;}};gl=getWebglCanvas();if(!gl){return null;}
var result=[];var vShaderTemplate='attribute vec2 attrVertex;varying vec2 varyinTexCoordinate;uniform vec2 uniformOffset;void main(){varyinTexCoordinate=attrVertex+uniformOffset;gl_Position=vec4(attrVertex,0,1);}';var fShaderTemplate='precision mediump float;varying vec2 varyinTexCoordinate;void main() {gl_FragColor=vec4(varyinTexCoordinate,0,1);}';var vertexPosBuffer=gl.createBuffer();gl.bindBuffer(gl.ARRAY_BUFFER,vertexPosBuffer);var vertices=new Float32Array([-0.2,-0.9,0,0.4,-0.26,0,0,0.732134444,0]);gl.bufferData(gl.ARRAY_BUFFER,vertices,gl.STATIC_DRAW);vertexPosBuffer.itemSize=3;vertexPosBuffer.numItems=3;var program=gl.createProgram();var vshader=gl.createShader(gl.VERTEX_SHADER);gl.shaderSource(vshader,vShaderTemplate);gl.compileShader(vshader);var fshader=gl.createShader(gl.FRAGMENT_SHADER);gl.shaderSource(fshader,fShaderTemplate);gl.compileShader(fshader);gl.attachShader(program,vshader);gl.attachShader(program,fshader);gl.linkProgram(program);gl.useProgram(program);program.vertexPosAttrib=gl.getAttribLocation(program,'attrVertex');program.offsetUniform=gl.getUniformLocation(program,'uniformOffset');gl.enableVertexAttribArray(program.vertexPosArray);gl.vertexAttribPointer(program.vertexPosAttrib,vertexPosBuffer.itemSize,gl.FLOAT,!1,0,0);gl.uniform2f(program.offsetUniform,1,1);gl.drawArrays(gl.TRIANGLE_STRIP,0,vertexPosBuffer.numItems);try{result.push(gl.canvas.toDataURL());}
catch(e){}
result.push('extensions:'+(gl.getSupportedExtensions()||[]).join(';'));result.push('webgl aliased line width range:'+fa2s(gl.getParameter(gl.ALIASED_LINE_WIDTH_RANGE)));result.push('webgl aliased point size range:'+fa2s(gl.getParameter(gl.ALIASED_POINT_SIZE_RANGE)));result.push('webgl alpha bits:'+gl.getParameter(gl.ALPHA_BITS));result.push('webgl antialiasing:'+(gl.getContextAttributes().antialias?'yes':'no'));result.push('webgl blue bits:'+gl.getParameter(gl.BLUE_BITS));result.push('webgl depth bits:'+gl.getParameter(gl.DEPTH_BITS));result.push('webgl green bits:'+gl.getParameter(gl.GREEN_BITS));result.push('webgl max anisotropy:'+maxAnisotropy(gl));result.push('webgl max combined texture image units:'+gl.getParameter(gl.MAX_COMBINED_TEXTURE_IMAGE_UNITS));result.push('webgl max cube map texture size:'+gl.getParameter(gl.MAX_CUBE_MAP_TEXTURE_SIZE));result.push('webgl max fragment uniform vectors:'+gl.getParameter(gl.MAX_FRAGMENT_UNIFORM_VECTORS));result.push('webgl max render buffer size:'+gl.getParameter(gl.MAX_RENDERBUFFER_SIZE));result.push('webgl max texture image units:'+gl.getParameter(gl.MAX_TEXTURE_IMAGE_UNITS));result.push('webgl max texture size:'+gl.getParameter(gl.MAX_TEXTURE_SIZE));result.push('webgl max varying vectors:'+gl.getParameter(gl.MAX_VARYING_VECTORS));result.push('webgl max vertex attribs:'+gl.getParameter(gl.MAX_VERTEX_ATTRIBS));result.push('webgl max vertex texture image units:'+gl.getParameter(gl.MAX_VERTEX_TEXTURE_IMAGE_UNITS));result.push('webgl max vertex uniform vectors:'+gl.getParameter(gl.MAX_VERTEX_UNIFORM_VECTORS));result.push('webgl max viewport dims:'+fa2s(gl.getParameter(gl.MAX_VIEWPORT_DIMS)));result.push('webgl red bits:'+gl.getParameter(gl.RED_BITS));result.push('webgl renderer:'+gl.getParameter(gl.RENDERER));result.push('webgl shading language version:'+gl.getParameter(gl.SHADING_LANGUAGE_VERSION));result.push('webgl stencil bits:'+gl.getParameter(gl.STENCIL_BITS));result.push('webgl vendor:'+gl.getParameter(gl.VENDOR));result.push('webgl version:'+gl.getParameter(gl.VERSION));try{var extensionDebugRendererInfo=gl.getExtension('WEBGL_debug_renderer_info');if(extensionDebugRendererInfo){result.push('webgl unmasked vendor:'+gl.getParameter(extensionDebugRendererInfo.UNMASKED_VENDOR_WEBGL));result.push('webgl unmasked renderer:'+gl.getParameter(extensionDebugRendererInfo.UNMASKED_RENDERER_WEBGL));}}
catch(e){}
if(!gl.getShaderPrecisionFormat){return result;}
each(['FLOAT','INT'],function(numType){each(['VERTEX','FRAGMENT'],function(shader){each(['HIGH','MEDIUM','LOW'],function(numSize){each(['precision','rangeMin','rangeMax'],function(key){var format=gl.getShaderPrecisionFormat(gl[shader+'_SHADER'],gl[numSize+'_'+numType])[key];if(key!=='precision'){key='precision '+key;}
var line=['webgl ',shader.toLowerCase(),' shader ',numSize.toLowerCase(),' ',numType.toLowerCase(),' ',key,':',format].join('');result.push(line);});});});});return result;};var getWebglVendorAndRenderer=function(){try{var glContext=getWebglCanvas();var extensionDebugRendererInfo=glContext.getExtension('WEBGL_debug_renderer_info');return glContext.getParameter(extensionDebugRendererInfo.UNMASKED_VENDOR_WEBGL)+'~'+glContext.getParameter(extensionDebugRendererInfo.UNMASKED_RENDERER_WEBGL);}
catch(e){return null;}};var getAdBlock=function(){var ads=document.createElement('div');ads.innerHTML='&nbsp;';ads.className='adsbox';var result=false;try{document.body.appendChild(ads);result=doc.getElementsByClassName('adsbox')[0].offsetHeight===0;document.body.removeChild(ads);}
catch(e){result=false;}
return result;};var getHasLiedLanguages=function(){if(typeof navigator.languages!=='undefined'){try{var firstLanguages=navigator.languages[0].substr(0,2);if(firstLanguages!==navigator.language.substr(0,2)){return true;}}
catch(err){return true;}}
return false;};var getHasLiedResolution=function(){return window.screen.width<window.screen.availWidth||window.screen.height<window.screen.availHeight;};var getHasLiedOs=function(){var userAgent=nav.userAgent.toLowerCase();var oscpu=nav.oscpu;var platform=nav.platform.toLowerCase();var os;if(userAgent.indexOf('windows phone')>=0){os='Windows Phone';}
else if(userAgent.indexOf('win')>=0){os='Windows';}
else if(userAgent.indexOf('android')>=0){os='Android';}
else if(userAgent.indexOf('linux')>=0){os='Linux';}
else if(userAgent.indexOf('iphone')>=0||userAgent.indexOf('ipad')>=0){os='iOS';}
else if(userAgent.indexOf('mac')>=0){os='Mac';}
else{os='Other';}
var mobileDevice=(('ontouchstart'in wnd)||(nav.maxTouchPoints>0)||(nav.msMaxTouchPoints>0));if(mobileDevice&&os!=='Windows Phone'&&os!=='Android'&&os!=='iOS'&&os!=='Other'){return true;}
if(typeof oscpu!=='undefined'){oscpu=oscpu.toLowerCase();if(oscpu.indexOf('win')>=0&&os!=='Windows'&&os!=='Windows Phone'){return true;}
else if(oscpu.indexOf('linux')>=0&&os!=='Linux'&&os!=='Android'){return true;}
else if(oscpu.indexOf('mac')>=0&&os!=='Mac'&&os!=='iOS'){return true;}
else if((oscpu.indexOf('win')===-1&&oscpu.indexOf('linux')===-1&&oscpu.indexOf('mac')===-1)!==(os==='Other')){return true;}}
if(platform.indexOf('win')>=0&&os!=='Windows'&&os!=='Windows Phone'){return true;}
else if((platform.indexOf('linux')>=0||platform.indexOf('android')>=0||platform.indexOf('pike')>=0)&&os!=='Linux'&&os!=='Android'){return true;}
else if((platform.indexOf('mac')>=0||platform.indexOf('ipad')>=0||platform.indexOf('ipod')>=0||platform.indexOf('iphone')>=0)&&os!=='Mac'&&os!=='iOS'){return true;}
else if((platform.indexOf('win')===-1&&platform.indexOf('linux')===-1&&platform.indexOf('mac')===-1)!==(os==='Other')){return true;}
return typeof navigator.plugins==='undefined'&&os!=='Windows'&&os!=='Windows Phone';};var getHasLiedBrowser=function(){var userAgent=navigator.userAgent.toLowerCase();var productSub=navigator.productSub;var browser;if(userAgent.indexOf('firefox')>=0){browser='Firefox';}
else if(userAgent.indexOf('opera')>=0||userAgent.indexOf('opr')>=0){browser='Opera';}
else if(userAgent.indexOf('chrome')>=0){browser='Chrome';}
else if(userAgent.indexOf('safari')>=0){browser='Safari';}
else if(userAgent.indexOf('trident')>=0){browser='Internet Explorer';}
else{browser='Other';}
if((browser==='Chrome'||browser==='Safari'||browser==='Opera')&&productSub!=='20030107'){return true;}
var tempRes=eval.toString().length;if(tempRes===37&&browser!=='Safari'&&browser!=='Firefox'&&browser!=='Other'){return true;}
else if(tempRes===39&&browser!=='Internet Explorer'&&browser!=='Other'){return true;}
else if(tempRes===33&&browser!=='Chrome'&&browser!=='Opera'&&browser!=='Other'){return true;}
var errFirefox;try{throw'a';}
catch(err){try{err.toSource();errFirefox=true;}
catch(errOfErr){errFirefox=false;}}
return errFirefox&&browser!=='Firefox'&&browser!=='Other';};var isCanvasSupported=function(){var elem=document.createElement('canvas');return!!(elem.getContext&&elem.getContext('2d'));};var isWebGlSupported=function(){if(!isCanvasSupported()){return false;}
var glContext=getWebglCanvas();return!!wnd.WebGLRenderingContext&&!!glContext;};var isIE=function(){if(navigator.appName==='Microsoft Internet Explorer'){return true;}
else if(navigator.appName==='Netscape'&&/Trident/.test(navigator.userAgent)){return true;}
return false;};var hasSwfObjectLoaded=function(){return typeof wnd.swfobject!=='undefined';};var hasMinFlashInstalled=function(){return wnd.swfobject.hasFlashPlayerVersion('9.0.0');};var addFlashDivNode=function(options){var node=document.createElement('div');node.setAttribute('id',options.fonts.swfContainerId);document.body.appendChild(node);};var loadSwfAndDetectFonts=function(done,options){var hiddenCallback='___fp_swf_loaded';window[hiddenCallback]=function(fonts){done(fonts);};var id=options.fonts.swfContainerId;addFlashDivNode(undefined);var flashvars={onReady:hiddenCallback};var flashparams={allowScriptAccess:'always',menu:'false'};wnd.swfobject.embedSWF(options.fonts.swfPath,id,'1','1','9.0.0',false,flashvars,flashparams,{});};var getWebglCanvas=function(){var canvas=document.createElement('canvas');var gl=null;try{gl=canvas.getContext('webgl')||canvas.getContext('experimental-webgl');}
catch(e){}
if(!gl){gl=null;}
return gl;};var components=[{key:'userAgent',getData:UserAgent},{key:'language',getData:languageKey},{key:'colorDepth',getData:colorDepthKey},{key:'deviceMemory',getData:deviceMemoryKey},{key:'pixelRatio',getData:pixelRatioKey},{key:'hardwareConcurrency',getData:hardwareConcurrencyKey},{key:'screenResolution',getData:screenResolutionKey},{key:'availableScreenResolution',getData:availableScreenResolutionKey},{key:'timezoneOffset',getData:timezoneOffset},{key:'timezone',getData:timezone},{key:'sessionStorage',getData:sessionStorageKey},{key:'localStorage',getData:localStorageKey},{key:'indexedDb',getData:indexedDbKey},{key:'addBehavior',getData:addBehaviorKey},{key:'openDatabase',getData:openDatabaseKey},{key:'cpuClass',getData:cpuClassKey},{key:'platform',getData:platformKey},{key:'doNotTrack',getData:doNotTrackKey},{key:'plugins',getData:pluginsComponent},{key:'canvas',getData:canvasKey},{key:'webgl',getData:webglKey},{key:'webglVendorAndRenderer',getData:webglVendorAndRendererKey},{key:'adBlock',getData:adBlockKey},{key:'hasLiedLanguages',getData:hasLiedLanguagesKey},{key:'hasLiedResolution',getData:hasLiedResolutionKey},{key:'hasLiedOs',getData:hasLiedOsKey},{key:'hasLiedBrowser',getData:hasLiedBrowserKey},{key:'touchSupport',getData:touchSupportKey},];var Fingerprint2=(function(){function Fingerprint2(){this.get=function(options,callback){if(!callback){callback=options;options={};}
else if(!options){options={};}
extendSoft(options,defaultOptions);options.components=options.extraComponents.concat(components);var keys={data:[],addPreprocessedComponent:function(key,value){if(typeof options.preprocessor==='function'){value=options.preprocessor(key,value);}
keys.data.push({key:key,value:value});}};var i=-1;var chainComponents=function(alreadyWaited){i+=1;if(i>=options.components.length){callback(keys.data);return;}
var component=options.components[i];if(options.excludes[component.key]){chainComponents(false);return;}
if(!alreadyWaited&&component.pauseBefore){i-=1;setTimeout(function(){chainComponents(true);},1);return;}
try{component.getData(function(value){keys.addPreprocessedComponent(component.key,value);chainComponents(false);},options);}
catch(error){keys.addPreprocessedComponent(component.key,String(error));chainComponents(false);}};chainComponents(false);};this.getPromise=function(options){return new Promise(function(resolve,reject){this.get(options,resolve);});};this.getV18=function(options,callback){if(callback==null){callback=options;options={};}
return this.get(options,function(components){var newComponents=[];for(var i=0;i<components.length;i++){var component=components[i];if(component.value===(options.NOT_AVAILABLE||'not available')){newComponents.push({key:component.key,value:'unknown'});}
else if(component.key==='plugins'){newComponents.push({key:'plugins',value:map(component.value,function(p){var mimeTypes=map(p[2],function(mt){if(mt.join){return mt.join('~');}
return mt;}).join(',');return[p[0],p[1],mimeTypes].join('::');})});}
else if(['canvas','webgl'].indexOf(component.key)!==-1){newComponents.push({key:component.key,value:component.value.join('~')});}
else if(['sessionStorage','localStorage','indexedDb','addBehavior','openDatabase'].indexOf(component.key)!==-1){if(component.value){newComponents.push({key:component.key,value:1});}
else{continue;}}
else{if(component.value){newComponents.push(component.value.join?{key:component.key,value:component.value.join(';')}:component);}
else{newComponents.push({key:component.key,value:component.value});}}}
var murmur=x64hash128(map(newComponents,function(component){return component.value;}).join('~~~'),31);callback(murmur,newComponents);});};this.x64hash128=x64hash128;this.VERSION='2.0.0';}
return Fingerprint2;}());exports.Fingerprint2=Fingerprint2;},{}],6:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var Http=(function(){function Http(){}
Http.post_statusData=function(url,data){return new Promise(function(resolve,reject){var request=new XMLHttpRequest();request.onload=function(){if(this.status>=200&&this.status<303){var res=[this.status,this.response];resolve(res);}
else{reject(new Error(this.statusText));}};request.onerror=function(){reject(new Error("(POST_COUNTER)XMLHttpRequest Error: "+this.statusText));};request.open("POST",url,true);request.setRequestHeader("Content-Type","application/json");request.send(JSON.stringify(data));});};return Http;}());exports.Http=Http;},{}],7:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});function wait_ga(wnd,timeout,callback){if(wnd.ga!=undefined){setTimeout(function(){callback();},1000);callback();return;}
setTimeout(function(){wait_ga(wnd,timeout,callback);},timeout);}
exports.wait_ga=wait_ga;},{}],8:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});function wait_ya(wnd,timeout,callback){var yaCounterX=Object.keys(wnd).filter(function(el){return/^yaCounter.*?/i.test(el);})[0];if(wnd.ym!=undefined||yaCounterX!=undefined){setTimeout(function(){callback();},1000);return;}
setTimeout(function(){wait_ya(wnd,timeout,callback);},timeout);}
exports.wait_ya=wait_ya;},{}],9:[function(require,module,exports){"use strict";Object.defineProperty(exports,"__esModule",{value:true});var fingerprint_1=require("./core/fingerprint");function fp(wnd){var bkd=wnd._b_data_;var fpData=fingerprint_1.Fingerprint.get();var obj={};var nav=navigator;function getAdBlock(){var ads=document.createElement("div");ads.innerHTML="&nbsp;";ads.className="adsbox";var result=false;try{document.body.appendChild(ads);result=0===document.getElementsByClassName("adsbox")[0].offsetHeight;document.body.removeChild(ads);}
catch(e){result=false;}
return result;}
function getDoNotTrack(){try{var msDontr=nav.msDoNotTrack;var navDontr=nav.doNotTrack;if(msDontr){if(msDontr=="0"){return false;}
else if(msDontr=="1"){return true;}
else if(msDontr=="yes"){return true;}
else if(msDontr=="no"){return false;}
else if(msDontr=="null"){return false;}
else
return false;}
else if(navDontr){if(navDontr=="0"){return false;}
else if(navDontr=="1"){return true;}
else if(navDontr=="unspecified"){return false;}
else if(navDontr=="null"){return false;}
else
return false;}
else{return false;}}
catch(_a){return false;}}
function getHasLiedLanguages(){if(typeof navigator.languages!=="undefined"){try{var firstLanguages=navigator.languages[0].substr(0,2);if(firstLanguages!==navigator.language.substr(0,2)){return true;}}
catch(err){return true;}}
return false;}
function getHasLiedResolution(){try{return(window.screen.width<window.screen.availWidth||window.screen.height<window.screen.availHeight);}
catch(_a){return null;}}
function getBuildId(){var win=window;try{var b=win.navigator.buildID;if(b==undefined||typeof(b)!="string"){b="undefined";}
return b;}
catch(_a){return null;}}
function getHasLiedOs(){var nav=navigator;var userAgent=nav.userAgent.toLowerCase();var oscpu=nav.oscpu;var platform=nav.platform.toLowerCase();var os;if(userAgent.indexOf("windows phone")>=0){os="Windows Phone";}
else if(userAgent.indexOf("win")>=0){os="Windows";}
else if(userAgent.indexOf("android")>=0){os="Android";}
else if(userAgent.indexOf("linux")>=0){os="Linux";}
else if(userAgent.indexOf("iphone")>=0||userAgent.indexOf("ipad")>=0){os="iOS";}
else if(userAgent.indexOf("mac")>=0){os="Mac";}
else{os="Other";}
var mobileDevice="ontouchstart"in window||nav.maxTouchPoints>0||nav.msMaxTouchPoints>0;if(mobileDevice&&os!=="Windows Phone"&&os!=="Android"&&os!=="iOS"&&os!=="Other"){return true;}
if(typeof oscpu!=="undefined"){oscpu=oscpu.toLowerCase();if(oscpu.indexOf("win")>=0&&os!=="Windows"&&os!=="Windows Phone"){return true;}
else if(oscpu.indexOf("linux")>=0&&os!=="Linux"&&os!=="Android"){return true;}
else if(oscpu.indexOf("mac")>=0&&os!=="Mac"&&os!=="iOS"){return true;}
else if((oscpu.indexOf("win")===-1&&oscpu.indexOf("linux")===-1&&oscpu.indexOf("mac")===-1)!==(os==="Other")){return true;}}
if(platform.indexOf("win")>=0&&os!=="Windows"&&os!=="Windows Phone"){return true;}
else if((platform.indexOf("linux")>=0||platform.indexOf("android")>=0||platform.indexOf("pike")>=0)&&os!=="Linux"&&os!=="Android"){return true;}
else if((platform.indexOf("mac")>=0||platform.indexOf("ipad")>=0||platform.indexOf("ipod")>=0||platform.indexOf("iphone")>=0)&&os!=="Mac"&&os!=="iOS"){return true;}
else if(os==="Other"&&platform.indexOf("mac")===-1&&platform.indexOf("ipad")===-1&&platform.indexOf("ipod")===-1&&platform.indexOf("iphone")===-1&&platform.indexOf("linux")===-1&&platform.indexOf("android")===-1&&platform.indexOf("pike")===-1&&platform.indexOf("win")===-1){return true;}
return(typeof navigator.plugins==="undefined"&&os!=="Windows"&&os!=="Windows Phone");}
function getHasLiedBrowser(){var nav=navigator;var userAgent=nav.userAgent.toLowerCase();var productSub=nav.productSub;var browser;if(userAgent.indexOf("firefox")>=0){browser="Firefox";}
else if(userAgent.indexOf("opera")>=0||userAgent.indexOf("opr")>=0){browser="Opera";}
else if(userAgent.indexOf("chrome")>=0){browser="Chrome";}
else if(userAgent.indexOf("safari")>=0){browser="Safari";}
else if(userAgent.indexOf("trident")>=0){browser="Internet Explorer";}
else{browser="Other";}
if((browser==="Chrome"||browser==="Safari"||browser==="Opera")&&productSub!=="20030107"){return true;}
var tempRes=eval.toString().length;if(tempRes===37&&browser!=="Safari"&&browser!=="Firefox"&&browser!=="Other"){return true;}
else if(tempRes===39&&browser!=="Internet Explorer"&&browser!=="Other"){return true;}
else if(tempRes===33&&browser!=="Chrome"&&browser!=="Opera"&&browser!=="Other"){return true;}
var errFirefox;try{throw"a";}
catch(err){try{err.toSource();errFirefox=true;}
catch(errOfErr){errFirefox=false;}}
return errFirefox&&browser!=="Firefox"&&browser!=="Other";}
function timezone(){try{var param=wnd.Intl.DateTimeFormat().resolvedOptions().timeZone;if(param==undefined){return"not available";}
else
return param;}
catch(_a){return"not available";}}
function timezoneOffset(){try{var timeOffset=new Date().getTimezoneOffset();if(timeOffset==undefined||typeof(timeOffset)!='number'){return null;}
else
return timeOffset;}
catch(_a){return null;}}
function getWebdriver(){try{var webdrv=navigator.webdriver;if(webdrv==undefined){return false;}
if(webdrv==true){return true;}
if(webdrv==false){return false;}
else{return null;}}
catch(_a){return null;}}
function getBodyStyle(){try{var bs=document.body.style;if("msTransform"in bs)
return"ms";else if("MozTransform"in bs&&"MozColumnCount"in bs&&"MozBorderImage"in bs&&"MozColumnGap"in bs)
return"moz";else if("OTransform"in bs)
return"opera";else
return"undefined";}
catch(_a){return null;}}
function getHiddenFunc(d){try{if(d.webkitHidden!=undefined)
return"webkitHidden";if(d.msHidden!=undefined)
return"msHidden";if(d.mozHidden!=undefined)
return"mozHidden";else
return"n/a";}
catch(_a){return"n/a";}}
for(var _i=0,_a=fpData.getComponents();_i<_a.length;_i++){var item=_a[_i];obj[item.key]=item.value;}
obj["adBlock"]=getAdBlock();obj["bodyStyle"]=getBodyStyle();obj["buildId"]=getBuildId();obj["clipboard"]=(nav.clipboard!=null)&&(nav.clipboard!=undefined);obj["doNotTrack"]=getDoNotTrack();obj["getBattery"]=!!("getBattery"in nav);obj["hasLiedLanguages"]=getHasLiedLanguages();obj["hasLiedResolution"]=getHasLiedResolution();obj["hasLiedOs"]=getHasLiedOs();obj["hasLiedBrowser"]=getHasLiedBrowser();obj["locationBar"]=(wnd.locationbar&&wnd.locationbar.visible)||wnd.locationbar!=undefined;obj["mozInnerScreen"]=("mozInnerScreenX"in wnd)&&("mozInnerScreenY"in wnd);obj["requestFileSystem"]=!!("webkitRequestFileSystem"in wnd);obj["timezone"]=timezone();obj["timezoneOffset"]=timezoneOffset();obj["userAgent"]=nav.userAgent;obj["webdriver"]=getWebdriver();obj["productSub"]=nav.productSub.toString();obj["phantom"]=!!(wnd.callPhantom||wnd._phantom);obj["node"]=wnd.Buffer!==undefined;obj["coach"]=wnd.emit!==undefined;obj["rhino"]=wnd.spawn!==undefined;obj["domAuto"]=wnd.domAutomation!==undefined||wnd.domAutomationController!==undefined;obj["online"]=wnd.navigator.onLine;obj["audio"]=null;obj["canvas"]=null;obj["webgl"]=null;obj["touchSupport"]=null;obj["pixelRatio"]=null;var d=document;obj["applePay"]=!!(wnd.ApplePaySession);obj["performance"]=!!(wnd.performance&&"function"==typeof performance.now);obj["topself"]=!!(window.top===window.self);obj["hiddenFunc"]=getHiddenFunc(d);obj["hasFocus"]="hasFocus"in document?document.hasFocus()?true:false:undefined;obj["sendBeacon"]=nav.sendBeacon?true:false;obj["hasCookie"]=!!(nav.cookieEnabled);obj["media"]=nav.mediaDevices&&nav.mediaDevices.getUserMedia?2:nav.getUserMedia?1:0;obj["mFullScreen"]=!!(d.mozCancelFullScreen||d.mozFullScreen||d.mozFullScreenElement||d.mozFullScreenEnabled||d.mozSetImageElement);obj["mRtci"]=!!(wnd.mozRTCIceCandidate||wnd.mozRTCPeerConnection||wnd.mozRTCSessionDescription);if(obj["fonts"]=="TypeError: Cannot read property 'appendChild' of undefined"){obj["fonts"]=["TypeError"];}
obj["uaDataMobile"]=null;obj["uaDataPlatform"]=null;obj["uaDataBrands"]=null;var uaData=nav.userAgentData;if(uaData!=undefined){obj["uaDataBrands"]=uaData.brands;obj["uaDataMobile"]=uaData.mobile;obj["uaDataPlatform"]=uaData.platform;}
obj["windowChrome"]=!!wnd.chrome;obj["doNotTrack"]=!!obj["doNotTrack"];if(typeof(obj["productSub"])!='string')
obj["productSub"]=obj["productSub"].toString();if(typeof(obj["deviceMemory"])!='number')
obj["deviceMemory"]=null;var scr=obj["screenResolution"];if(typeof(scr[0])!='number'||typeof(scr[1])!='number')
obj["screenResolution"]=[0,0];if(typeof(obj["hardwareConcurrency"])!='number')
obj["hardwareConcurrency"]=null;bkd.fpr=obj;bkd.has=fpData.getValue();}
exports.fp=fp;},{"./core/fingerprint":4}],10:[function(require,module,exports){"use strict";var __awaiter=(this&&this.__awaiter)||function(thisArg,_arguments,P,generator){return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value));}catch(e){reject(e);}}
function rejected(value){try{step(generator["throw"](value));}catch(e){reject(e);}}
function step(result){result.done?resolve(result.value):new P(function(resolve){resolve(result.value);}).then(fulfilled,rejected);}
step((generator=generator.apply(thisArg,_arguments||[])).next());});};var __generator=(this&&this.__generator)||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1];},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),"throw":verb(1),"return":verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this;}),g;function verb(n){return function(v){return step([n,v]);};}
function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=y[op[0]&2?"return":op[0]?"throw":"next"])&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[0,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue;}
if(op[0]===3&&(!t||(op[1]>t[0]&&op[1]<t[3]))){_.label=op[1];break;}
if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break;}
if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break;}
if(t[2])_.ops.pop();_.trys.pop();continue;}
op=body.call(thisArg,_);}catch(e){op=[6,e];y=0;}finally{f=t=0;}
if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true};}};Object.defineProperty(exports,"__esModule",{value:true});function getAnalyze(wnd){return __awaiter(this,void 0,void 0,function(){var bkd,_u_analytics_,_y_metrika_,isBot,isNobot,isFraud,isBux,isClean,param,cats,cat,h_dimension;return __generator(this,function(_a){bkd=wnd._b_data_;_u_analytics_=bkd.ga4;_y_metrika_=bkd.ycr;isBot=(bkd.oos==1||bkd.oot==true);isNobot=!!(bkd.oos==0)&&!isBot;isFraud=!!(bkd.oos==2);isBux=!!(bkd.oox==true);isClean=!!(bkd.oos==3);param=null;if(isClean){param="clean";}
else if(isNobot){param="nobot";}
else if(isFraud){param="suspect";}
else if(isBot){param="bot";}
if(bkd.srv==1&&typeof(wnd.gtag)==='function'){if(param!=null){if(isBot&&isBux){wnd.ym(_y_metrika_,"params",{"Botfaqtor":'bot'});wnd.ym(_y_metrika_,"params",{"Botfaqtor":'bux'});bkd.yps=1;wnd.gtag("event","page_view",{"Botfaqtor":'bot'});wnd.gtag("event","page_view",{"Botfaqtor":'bux'});bkd.gps=1;}
else{wnd.ym(_y_metrika_,"params",{"Botfaqtor":param});bkd.yps=1;wnd.gtag("event","page_view",{"Botfaqtor":param});bkd.gps=1;}
if(isBot){eval("_tmr.push({type: 'reachGoal', id: 3375222, goal: 'bot'});");bkd.vps=1;}}
if(bkd.ooc!=null){if((isNobot||isClean)&&bkd.ooc.length>0){cats=bkd.ooc;for(cat in cats){wnd.ym(_y_metrika_,"params",{"Botfaqtor":{"Category":cat}});bkd.ycs=1;}}}
if(bkd.uuu!=""){wnd.ym(_y_metrika_,"setUserID",bkd.uuu);}}
if(bkd.srv==0&&param!=null){h_dimension={};h_dimension[bkd.gdb]="Honest-Metrics";wnd.gtag("config",_u_analytics_,{custom_map:h_dimension});wnd.gtag("event","pageview",{"Honest-Metrics":param});}
return[2];});});}
exports.getAnalyze=getAnalyze;},{}],11:[function(require,module,exports){"use strict";var __awaiter=(this&&this.__awaiter)||function(thisArg,_arguments,P,generator){return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value));}catch(e){reject(e);}}
function rejected(value){try{step(generator["throw"](value));}catch(e){reject(e);}}
function step(result){result.done?resolve(result.value):new P(function(resolve){resolve(result.value);}).then(fulfilled,rejected);}
step((generator=generator.apply(thisArg,_arguments||[])).next());});};var __generator=(this&&this.__generator)||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1];},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),"throw":verb(1),"return":verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this;}),g;function verb(n){return function(v){return step([n,v]);};}
function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=y[op[0]&2?"return":op[0]?"throw":"next"])&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[0,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue;}
if(op[0]===3&&(!t||(op[1]>t[0]&&op[1]<t[3]))){_.label=op[1];break;}
if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break;}
if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break;}
if(t[2])_.ops.pop();_.trys.pop();continue;}
op=body.call(thisArg,_);}catch(e){op=[6,e];y=0;}finally{f=t=0;}
if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true};}};Object.defineProperty(exports,"__esModule",{value:true});var fp_1=require("./fp");var options_1=require("./core/evercookie/options");var cookie_1=require("./core/evercookie/sources/cookie");var status_async_1=require("./status_async");var wait_ya_1=require("./core/wait_ya");var wait_ga_1=require("./core/wait_ga");var getAnalyze_1=require("./getAnalyze");var _locate=location.href;if(_locate.indexOf(".ru")>=0){var wnd=window;wnd._b_data_={sid:829,srv:1,uri:"5-182-5-41",gcr:"UA-160917634-2",ga4:"G-W3TJZ8K0PP",ycr:57157849,gdb:"dimension1",gdc:"dimension2",};}
else if(_locate.indexOf(".com")>=0){var wnd=window;wnd._b_data_={sid:1403,srv:0,uri:"5-182-5-41",gcr:"UA-185912251-1",gdb:"dimension1",};}
function getJsonFromUrl(url){if(!url)
url=location.href;var question=url.indexOf("?");var hash=url.indexOf("#");if(hash==-1&&question==-1)
return{};if(hash==-1)
hash=url.length;var query=question==-1||hash==question+1?url.substring(hash):url.substring(question+1,hash);var result={};query.split("&").forEach(function(part){if(!part)
return;part=part.split("+").join(" ");var eq=part.indexOf("=");var key=eq>-1?part.substr(0,eq):part;var val=eq>-1?decodeURIComponent(part.substr(eq+1)):"";var from=key.indexOf("[");if(from==-1)
result[decodeURIComponent(key)]=val;else{var to=key.indexOf("]",from);var index=decodeURIComponent(key.substring(from+1,to));key=decodeURIComponent(key.substring(0,from));if(!result[key])
result[key]=[];if(!index)
result[key].push(val);else
result[key][index]=val;}});return result;}
function getUid(){var cookie=new cookie_1.Cookie(new options_1.EverCookieOptions());var uid;var cook=document.cookie;if(cook!=null){if(cook.indexOf("ab_id=")>-1){uid=cook.split("ab_id=")[1];if(uid.indexOf(";")>-1){uid=uid.split(";")[0];}}}
if(uid=="00000000-0000-0000-0000-000000000000"){cookie.delete("ab_id");return undefined;}
return uid;}
run_async(window);function run_async(wnd){return __awaiter(this,void 0,void 0,function(){var bkd,urlParams;return __generator(this,function(_a){switch(_a.label){case 0:bkd=wnd._b_data_;bkd.uuu=getUid();fp_1.fp(wnd);return[4,status_async_1.get_status_async(wnd)];case 1:_a.sent();if(bkd.srv==0){wait_ga_1.wait_ga(wnd,500,function(){if(bkd.srv==1){wait_ya_1.wait_ya(wnd,500,function(){getAnalyze_1.getAnalyze(wnd);});}
else{getAnalyze_1.getAnalyze(wnd);}});}
else{wait_ya_1.wait_ya(wnd,500,function(){getAnalyze_1.getAnalyze(wnd);});}
urlParams=getJsonFromUrl(location.href);if(urlParams['g']==1){loadGoogleAnalytics();}
return[2];}});});}
var loadGoogleAnalytics=function(){try{var wnd=window;var ga4Id=wnd._b_data_.ga4||'G-W3TJZ8K0PP';window.dataLayer=window.dataLayer||[];window.gtag=function(){window.dataLayer.push(arguments);};var gaScript=document.createElement('script');gaScript.async=true;gaScript.src="https://www.googletagmanager.com/gtag/js?id="+ga4Id;document.head.appendChild(gaScript);window.gtag('js',new Date());window.gtag('config',ga4Id);}
catch(error){console.log('Ошибка:',error);}};},{"./core/evercookie/options":2,"./core/evercookie/sources/cookie":3,"./core/wait_ga":7,"./core/wait_ya":8,"./fp":9,"./getAnalyze":10,"./status_async":12}],12:[function(require,module,exports){"use strict";var __awaiter=(this&&this.__awaiter)||function(thisArg,_arguments,P,generator){return new(P||(P=Promise))(function(resolve,reject){function fulfilled(value){try{step(generator.next(value));}catch(e){reject(e);}}
function rejected(value){try{step(generator["throw"](value));}catch(e){reject(e);}}
function step(result){result.done?resolve(result.value):new P(function(resolve){resolve(result.value);}).then(fulfilled,rejected);}
step((generator=generator.apply(thisArg,_arguments||[])).next());});};var __generator=(this&&this.__generator)||function(thisArg,body){var _={label:0,sent:function(){if(t[0]&1)throw t[1];return t[1];},trys:[],ops:[]},f,y,t,g;return g={next:verb(0),"throw":verb(1),"return":verb(2)},typeof Symbol==="function"&&(g[Symbol.iterator]=function(){return this;}),g;function verb(n){return function(v){return step([n,v]);};}
function step(op){if(f)throw new TypeError("Generator is already executing.");while(_)try{if(f=1,y&&(t=y[op[0]&2?"return":op[0]?"throw":"next"])&&!(t=t.call(y,op[1])).done)return t;if(y=0,t)op=[0,t.value];switch(op[0]){case 0:case 1:t=op;break;case 4:_.label++;return{value:op[1],done:false};case 5:_.label++;y=op[1];op=[0];continue;case 7:op=_.ops.pop();_.trys.pop();continue;default:if(!(t=_.trys,t=t.length>0&&t[t.length-1])&&(op[0]===6||op[0]===2)){_=0;continue;}
if(op[0]===3&&(!t||(op[1]>t[0]&&op[1]<t[3]))){_.label=op[1];break;}
if(op[0]===6&&_.label<t[1]){_.label=t[1];t=op;break;}
if(t&&_.label<t[2]){_.label=t[2];_.ops.push(op);break;}
if(t[2])_.ops.pop();_.trys.pop();continue;}
op=body.call(thisArg,_);}catch(e){op=[6,e];y=0;}finally{f=t=0;}
if(op[0]&5)throw op[1];return{value:op[0]?op[1]:void 0,done:true};}};Object.defineProperty(exports,"__esModule",{value:true});var client_1=require("./core/client");var options_1=require("./core/evercookie/options");var cookie_1=require("./core/evercookie/sources/cookie");function get_status_async(wnd){return __awaiter(this,void 0,void 0,function(){var bkd,_client,uri,status_data,parse,cookie;return __generator(this,function(_a){switch(_a.label){case 0:bkd=wnd._b_data_;_client=new client_1.Client(bkd.uri,bkd.sid);uri=null;if(bkd.srv==0){uri="https://"+bkd.uri+".witstroom.com/b/h";}
else if(bkd.srv==1){uri="https://"+bkd.uri+".botfaqtor.ru/b/s";}
return[4,_client.check(uri,bkd.has,location.href,bkd.fpr)];case 1:status_data=_a.sent();if(typeof status_data==="object"){if(status_data[0]==200){if(status_data[1]){parse=JSON.parse(status_data[1]);;bkd.ooc=parse.c;bkd.oos=parse.s;bkd.oox=parse.x||false;bkd.oot=parse.t||false;bkd.uuu=bkd.uuu||parse.u;}}}
cookie=new cookie_1.Cookie(new options_1.EverCookieOptions());if(bkd.uuu&&bkd.uuu!="00000000-0000-0000-0000-000000000000"&&bkd.uuu!=""){cookie.set("ab_id",bkd.uuu);}
return[2];}});});}
exports.get_status_async=get_status_async;},{"./core/client":1,"./core/evercookie/options":2,"./core/evercookie/sources/cookie":3}],13:[function(require,module,exports){var objectCreate=Object.create||objectCreatePolyfill
var objectKeys=Object.keys||objectKeysPolyfill
var bind=Function.prototype.bind||functionBindPolyfill
function EventEmitter(){if(!this._events||!Object.prototype.hasOwnProperty.call(this,'_events')){this._events=objectCreate(null);this._eventsCount=0;}
this._maxListeners=this._maxListeners||undefined;}
module.exports=EventEmitter;EventEmitter.EventEmitter=EventEmitter;EventEmitter.prototype._events=undefined;EventEmitter.prototype._maxListeners=undefined;var defaultMaxListeners=10;var hasDefineProperty;try{var o={};if(Object.defineProperty)Object.defineProperty(o,'x',{value:0});hasDefineProperty=o.x===0;}catch(err){hasDefineProperty=false}
if(hasDefineProperty){Object.defineProperty(EventEmitter,'defaultMaxListeners',{enumerable:true,get:function(){return defaultMaxListeners;},set:function(arg){if(typeof arg!=='number'||arg<0||arg!==arg)
throw new TypeError('"defaultMaxListeners" must be a positive number');defaultMaxListeners=arg;}});}else{EventEmitter.defaultMaxListeners=defaultMaxListeners;}
EventEmitter.prototype.setMaxListeners=function setMaxListeners(n){if(typeof n!=='number'||n<0||isNaN(n))
throw new TypeError('"n" argument must be a positive number');this._maxListeners=n;return this;};function $getMaxListeners(that){if(that._maxListeners===undefined)
return EventEmitter.defaultMaxListeners;return that._maxListeners;}
EventEmitter.prototype.getMaxListeners=function getMaxListeners(){return $getMaxListeners(this);};function emitNone(handler,isFn,self){if(isFn)
handler.call(self);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)
listeners[i].call(self);}}
function emitOne(handler,isFn,self,arg1){if(isFn)
handler.call(self,arg1);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)
listeners[i].call(self,arg1);}}
function emitTwo(handler,isFn,self,arg1,arg2){if(isFn)
handler.call(self,arg1,arg2);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)
listeners[i].call(self,arg1,arg2);}}
function emitThree(handler,isFn,self,arg1,arg2,arg3){if(isFn)
handler.call(self,arg1,arg2,arg3);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)
listeners[i].call(self,arg1,arg2,arg3);}}
function emitMany(handler,isFn,self,args){if(isFn)
handler.apply(self,args);else{var len=handler.length;var listeners=arrayClone(handler,len);for(var i=0;i<len;++i)
listeners[i].apply(self,args);}}
EventEmitter.prototype.emit=function emit(type){var er,handler,len,args,i,events;var doError=(type==='error');events=this._events;if(events)
doError=(doError&&events.error==null);else if(!doError)
return false;if(doError){if(arguments.length>1)
er=arguments[1];if(er instanceof Error){throw er;}else{var err=new Error('Unhandled "error" event. ('+er+')');err.context=er;throw err;}
return false;}
handler=events[type];if(!handler)
return false;var isFn=typeof handler==='function';len=arguments.length;switch(len){case 1:emitNone(handler,isFn,this);break;case 2:emitOne(handler,isFn,this,arguments[1]);break;case 3:emitTwo(handler,isFn,this,arguments[1],arguments[2]);break;case 4:emitThree(handler,isFn,this,arguments[1],arguments[2],arguments[3]);break;default:args=new Array(len-1);for(i=1;i<len;i++)
args[i-1]=arguments[i];emitMany(handler,isFn,this,args);}
return true;};function _addListener(target,type,listener,prepend){var m;var events;var existing;if(typeof listener!=='function')
throw new TypeError('"listener" argument must be a function');events=target._events;if(!events){events=target._events=objectCreate(null);target._eventsCount=0;}else{if(events.newListener){target.emit('newListener',type,listener.listener?listener.listener:listener);events=target._events;}
existing=events[type];}
if(!existing){existing=events[type]=listener;++target._eventsCount;}else{if(typeof existing==='function'){existing=events[type]=prepend?[listener,existing]:[existing,listener];}else{if(prepend){existing.unshift(listener);}else{existing.push(listener);}}
if(!existing.warned){m=$getMaxListeners(target);if(m&&m>0&&existing.length>m){existing.warned=true;var w=new Error('Possible EventEmitter memory leak detected. '+
existing.length+' "'+String(type)+'" listeners '+'added. Use emitter.setMaxListeners() to '+'increase limit.');w.name='MaxListenersExceededWarning';w.emitter=target;w.type=type;w.count=existing.length;if(typeof console==='object'&&console.warn){console.warn('%s: %s',w.name,w.message);}}}}
return target;}
EventEmitter.prototype.addListener=function addListener(type,listener){return _addListener(this,type,listener,false);};EventEmitter.prototype.on=EventEmitter.prototype.addListener;EventEmitter.prototype.prependListener=function prependListener(type,listener){return _addListener(this,type,listener,true);};function onceWrapper(){if(!this.fired){this.target.removeListener(this.type,this.wrapFn);this.fired=true;switch(arguments.length){case 0:return this.listener.call(this.target);case 1:return this.listener.call(this.target,arguments[0]);case 2:return this.listener.call(this.target,arguments[0],arguments[1]);case 3:return this.listener.call(this.target,arguments[0],arguments[1],arguments[2]);default:var args=new Array(arguments.length);for(var i=0;i<args.length;++i)
args[i]=arguments[i];this.listener.apply(this.target,args);}}}
function _onceWrap(target,type,listener){var state={fired:false,wrapFn:undefined,target:target,type:type,listener:listener};var wrapped=bind.call(onceWrapper,state);wrapped.listener=listener;state.wrapFn=wrapped;return wrapped;}
EventEmitter.prototype.once=function once(type,listener){if(typeof listener!=='function')
throw new TypeError('"listener" argument must be a function');this.on(type,_onceWrap(this,type,listener));return this;};EventEmitter.prototype.prependOnceListener=function prependOnceListener(type,listener){if(typeof listener!=='function')
throw new TypeError('"listener" argument must be a function');this.prependListener(type,_onceWrap(this,type,listener));return this;};EventEmitter.prototype.removeListener=function removeListener(type,listener){var list,events,position,i,originalListener;if(typeof listener!=='function')
throw new TypeError('"listener" argument must be a function');events=this._events;if(!events)
return this;list=events[type];if(!list)
return this;if(list===listener||list.listener===listener){if(--this._eventsCount===0)
this._events=objectCreate(null);else{delete events[type];if(events.removeListener)
this.emit('removeListener',type,list.listener||listener);}}else if(typeof list!=='function'){position=-1;for(i=list.length-1;i>=0;i--){if(list[i]===listener||list[i].listener===listener){originalListener=list[i].listener;position=i;break;}}
if(position<0)
return this;if(position===0)
list.shift();else
spliceOne(list,position);if(list.length===1)
events[type]=list[0];if(events.removeListener)
this.emit('removeListener',type,originalListener||listener);}
return this;};EventEmitter.prototype.removeAllListeners=function removeAllListeners(type){var listeners,events,i;events=this._events;if(!events)
return this;if(!events.removeListener){if(arguments.length===0){this._events=objectCreate(null);this._eventsCount=0;}else if(events[type]){if(--this._eventsCount===0)
this._events=objectCreate(null);else
delete events[type];}
return this;}
if(arguments.length===0){var keys=objectKeys(events);var key;for(i=0;i<keys.length;++i){key=keys[i];if(key==='removeListener')continue;this.removeAllListeners(key);}
this.removeAllListeners('removeListener');this._events=objectCreate(null);this._eventsCount=0;return this;}
listeners=events[type];if(typeof listeners==='function'){this.removeListener(type,listeners);}else if(listeners){for(i=listeners.length-1;i>=0;i--){this.removeListener(type,listeners[i]);}}
return this;};function _listeners(target,type,unwrap){var events=target._events;if(!events)
return[];var evlistener=events[type];if(!evlistener)
return[];if(typeof evlistener==='function')
return unwrap?[evlistener.listener||evlistener]:[evlistener];return unwrap?unwrapListeners(evlistener):arrayClone(evlistener,evlistener.length);}
EventEmitter.prototype.listeners=function listeners(type){return _listeners(this,type,true);};EventEmitter.prototype.rawListeners=function rawListeners(type){return _listeners(this,type,false);};EventEmitter.listenerCount=function(emitter,type){if(typeof emitter.listenerCount==='function'){return emitter.listenerCount(type);}else{return listenerCount.call(emitter,type);}};EventEmitter.prototype.listenerCount=listenerCount;function listenerCount(type){var events=this._events;if(events){var evlistener=events[type];if(typeof evlistener==='function'){return 1;}else if(evlistener){return evlistener.length;}}
return 0;}
EventEmitter.prototype.eventNames=function eventNames(){return this._eventsCount>0?Reflect.ownKeys(this._events):[];};function spliceOne(list,index){for(var i=index,k=i+1,n=list.length;k<n;i+=1,k+=1)
list[i]=list[k];list.pop();}
function arrayClone(arr,n){var copy=new Array(n);for(var i=0;i<n;++i)
copy[i]=arr[i];return copy;}
function unwrapListeners(arr){var ret=new Array(arr.length);for(var i=0;i<ret.length;++i){ret[i]=arr[i].listener||arr[i];}
return ret;}
function objectCreatePolyfill(proto){var F=function(){};F.prototype=proto;return new F;}
function objectKeysPolyfill(obj){var keys=[];for(var k in obj)if(Object.prototype.hasOwnProperty.call(obj,k)){keys.push(k);}
return k;}
function functionBindPolyfill(context){var fn=this;return function(){return fn.apply(context,arguments);};}},{}],14:[function(require,module,exports){/*!@license
  UAParser.js v0.7.28
  Lightweight JavaScript-based User-Agent string parser
  https://github.com/faisalman/ua-parser-js
 
  Copyright © 2012-2021 Faisal Salman <<EMAIL>>
  Licensed under MIT License
 */(function(window,undefined){'use strict';var LIBVERSION='0.7.28',EMPTY='',UNKNOWN='?',FUNC_TYPE='function',UNDEF_TYPE='undefined',OBJ_TYPE='object',STR_TYPE='string',MAJOR='major',MODEL='model',NAME='name',TYPE='type',VENDOR='vendor',VERSION='version',ARCHITECTURE='architecture',CONSOLE='console',MOBILE='mobile',TABLET='tablet',SMARTTV='smarttv',WEARABLE='wearable',EMBEDDED='embedded',UA_MAX_LENGTH=255;var util={extend:function(regexes,extensions){var mergedRegexes={};for(var i in regexes){if(extensions[i]&&extensions[i].length%2===0){mergedRegexes[i]=extensions[i].concat(regexes[i]);}else{mergedRegexes[i]=regexes[i];}}
return mergedRegexes;},has:function(str1,str2){return typeof str1===STR_TYPE?str2.toLowerCase().indexOf(str1.toLowerCase())!==-1:false;},lowerize:function(str){return str.toLowerCase();},major:function(version){return typeof(version)===STR_TYPE?version.replace(/[^\d\.]/g,'').split(".")[0]:undefined;},trim:function(str,len){str=str.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,'');return typeof(len)===UNDEF_TYPE?str:str.substring(0,UA_MAX_LENGTH);}};var mapper={rgx:function(ua,arrays){var i=0,j,k,p,q,matches,match;while(i<arrays.length&&!matches){var regex=arrays[i],props=arrays[i+1];j=k=0;while(j<regex.length&&!matches){matches=regex[j++].exec(ua);if(!!matches){for(p=0;p<props.length;p++){match=matches[++k];q=props[p];if(typeof q===OBJ_TYPE&&q.length>0){if(q.length==2){if(typeof q[1]==FUNC_TYPE){this[q[0]]=q[1].call(this,match);}else{this[q[0]]=q[1];}}else if(q.length==3){if(typeof q[1]===FUNC_TYPE&&!(q[1].exec&&q[1].test)){this[q[0]]=match?q[1].call(this,match,q[2]):undefined;}else{this[q[0]]=match?match.replace(q[1],q[2]):undefined;}}else if(q.length==4){this[q[0]]=match?q[3].call(this,match.replace(q[1],q[2])):undefined;}}else{this[q]=match?match:undefined;}}}}
i+=2;}},str:function(str,map){for(var i in map){if(typeof map[i]===OBJ_TYPE&&map[i].length>0){for(var j=0;j<map[i].length;j++){if(util.has(map[i][j],str)){return(i===UNKNOWN)?undefined:i;}}}else if(util.has(map[i],str)){return(i===UNKNOWN)?undefined:i;}}
return str;}};var maps={browser:{oldSafari:{version:{'1.0':'/8','1.2':'/1','1.3':'/3','2.0':'/412','2.0.2':'/416','2.0.3':'/417','2.0.4':'/419','?':'/'}},oldEdge:{version:{'0.1':'12.','21':'13.','31':'14.','39':'15.','41':'16.','42':'17.','44':'18.'}}},os:{windows:{version:{'ME':'4.90','NT 3.11':'NT3.51','NT 4.0':'NT4.0','2000':'NT 5.0','XP':['NT 5.1','NT 5.2'],'Vista':'NT 6.0','7':'NT 6.1','8':'NT 6.2','8.1':'NT 6.3','10':['NT 6.4','NT 10.0'],'RT':'ARM'}}}};var regexes={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[VERSION,[NAME,'Chrome']],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[VERSION,[NAME,'Edge']],[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i,/(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i,],[NAME,VERSION],[/opios[\/\s]+([\w\.]+)/i],[VERSION,[NAME,'Opera Mini']],[/\sopr\/([\w\.]+)/i],[VERSION,[NAME,'Opera']],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(ba?idubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,/(weibo)__([\d\.]+)/i],[NAME,VERSION],[/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[VERSION,[NAME,'UCBrowser']],[/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],[VERSION,[NAME,'WeChat(Win) Desktop']],[/micromessenger\/([\w\.]+)/i],[VERSION,[NAME,'WeChat']],[/konqueror\/([\w\.]+)/i],[VERSION,[NAME,'Konqueror']],[/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],[VERSION,[NAME,'IE']],[/yabrowser\/([\w\.]+)/i],[VERSION,[NAME,'Yandex']],[/(avast|avg)\/([\w\.]+)/i],[[NAME,/(.+)/,'$1 Secure Browser'],VERSION],[/focus\/([\w\.]+)/i],[VERSION,[NAME,'Firefox Focus']],[/opt\/([\w\.]+)/i],[VERSION,[NAME,'Opera Touch']],[/coc_coc_browser\/([\w\.]+)/i],[VERSION,[NAME,'Coc Coc']],[/dolfin\/([\w\.]+)/i],[VERSION,[NAME,'Dolphin']],[/coast\/([\w\.]+)/i],[VERSION,[NAME,'Opera Coast']],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[VERSION,[NAME,'MIUI Browser']],[/fxios\/([\w\.-]+)/i],[VERSION,[NAME,'Firefox']],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[NAME,'360 Browser']],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[NAME,/(.+)/,'$1 Browser'],VERSION],[/(comodo_dragon)\/([\w\.]+)/i],[[NAME,/_/g,' '],VERSION],[/\s(electron)\/([\w\.]+)\ssafari/i,/(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[NAME,VERSION],[/(MetaSr)[\/\s]?([\w\.]+)/i,/(LBBROWSER)/i],[NAME],[/;fbav\/([\w\.]+);/i],[VERSION,[NAME,'Facebook']],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[NAME,'Facebook']],[/safari\s(line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/\s]([\w\.-]+)/i],[NAME,VERSION],[/\bgsa\/([\w\.]+)\s.*safari\//i],[VERSION,[NAME,'GSA']],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[VERSION,[NAME,'Chrome Headless']],[/\swv\).+(chrome)\/([\w\.]+)/i],[[NAME,'Chrome WebView'],VERSION],[/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],[VERSION,[NAME,'Android Browser']],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[NAME,VERSION],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[VERSION,[NAME,'Mobile Safari']],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[VERSION,NAME],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[NAME,[VERSION,mapper.str,maps.browser.oldSafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[NAME,VERSION],[/(navigator|netscape)\/([\w\.-]+)/i],[[NAME,'Netscape'],VERSION],[/ile\svr;\srv:([\w\.]+)\).+firefox/i],[VERSION,[NAME,'Firefox Reality']],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[NAME,VERSION]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[ARCHITECTURE,'amd64']],[/(ia32(?=;))/i],[[ARCHITECTURE,util.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[ARCHITECTURE,'ia32']],[/\b(aarch64|armv?8e?l?)\b/i],[[ARCHITECTURE,'arm64']],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[ARCHITECTURE,'armhf']],[/windows\s(ce|mobile);\sppc;/i],[[ARCHITECTURE,'arm']],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[ARCHITECTURE,/ower/,'',util.lowerize]],[/(sun4\w)[;\)]/i],[[ARCHITECTURE,'sparc']],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?:64|(?=v(?:[1-7]|[5-7]1)l?|;|eabi))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[ARCHITECTURE,util.lowerize]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],[MODEL,[VENDOR,'Samsung'],[TYPE,TABLET]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i,/\ssamsung[\s-]([\w-]+)/i,/sec-(sgh\w+)/i],[MODEL,[VENDOR,'Samsung'],[TYPE,MOBILE]],[/\((ip(?:hone|od)[\s\w]*);/i],[MODEL,[VENDOR,'Apple'],[TYPE,MOBILE]],[/\((ipad);[\w\s\),;-]+apple/i,/applecoremedia\/[\w\.]+\s\((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[MODEL,[VENDOR,'Apple'],[TYPE,TABLET]],[/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i,],[MODEL,[VENDOR,'Huawei'],[TYPE,TABLET]],[/d\/huawei([\w\s-]+)[;\)]/i,/\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,/\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i],[MODEL,[VENDOR,'Huawei'],[TYPE,MOBILE]],[/\b(poco[\s\w]+)(?:\sbuild|\))/i,/\b;\s(\w+)\sbuild\/hm\1/i,/\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,/\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[MODEL,/_/g,' '],[VENDOR,'Xiaomi'],[TYPE,MOBILE]],[/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],[[MODEL,/_/g,' '],[VENDOR,'Xiaomi'],[TYPE,TABLET]],[/;\s(\w+)\sbuild.+\soppo/i,/\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[MODEL,[VENDOR,'OPPO'],[TYPE,MOBILE]],[/\svivo\s(\w+)(?:\sbuild|\))/i,/\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],[MODEL,[VENDOR,'Vivo'],[TYPE,MOBILE]],[/\s(rmx[12]\d{3})(?:\sbuild|;)/i],[MODEL,[VENDOR,'Realme'],[TYPE,MOBILE]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,/\smot(?:orola)?[\s-](\w*)/i,/((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i],[MODEL,[VENDOR,'Motorola'],[TYPE,MOBILE]],[/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[MODEL,[VENDOR,'Motorola'],[TYPE,TABLET]],[/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],[MODEL,[VENDOR,'LG'],[TYPE,TABLET]],[/(lm-?f100[nv]?|nexus\s[45])/i,/lg[e;\s\/-]+((?!browser|netcast)\w+)/i,/\blg(\-?[\d\w]+)\sbuild/i],[MODEL,[VENDOR,'LG'],[TYPE,MOBILE]],[/(ideatab[\w\-\s]+)/i,/lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],[MODEL,[VENDOR,'Lenovo'],[TYPE,TABLET]],[/(?:maemo|nokia).*(n900|lumia\s\d+)/i,/nokia[\s_-]?([\w\.-]*)/i],[[MODEL,/_/g,' '],[VENDOR,'Nokia'],[TYPE,MOBILE]],[/droid.+;\s(pixel\sc)[\s)]/i],[MODEL,[VENDOR,'Google'],[TYPE,TABLET]],[/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],[MODEL,[VENDOR,'Google'],[TYPE,MOBILE]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[MODEL,[VENDOR,'Sony'],[TYPE,MOBILE]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[MODEL,'Xperia Tablet'],[VENDOR,'Sony'],[TYPE,TABLET]],[/\s(kb2005|in20[12]5|be20[12][59])\b/i,/\ba000(1)\sbuild/i,/\boneplus\s(a\d{4})[\s)]/i],[MODEL,[VENDOR,'OnePlus'],[TYPE,MOBILE]],[/(alexa)webm/i,/(kf[a-z]{2}wi)(\sbuild\/|\))/i,/(kf[a-z]+)(\sbuild\/|\)).+silk\//i],[MODEL,[VENDOR,'Amazon'],[TYPE,TABLET]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[MODEL,'Fire Phone'],[VENDOR,'Amazon'],[TYPE,MOBILE]],[/\((playbook);[\w\s\),;-]+(rim)/i],[MODEL,VENDOR,[TYPE,TABLET]],[/((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10;\s(\w+)/i],[MODEL,[VENDOR,'BlackBerry'],[TYPE,MOBILE]],[/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],[MODEL,[VENDOR,'ASUS'],[TYPE,TABLET]],[/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],[MODEL,[VENDOR,'ASUS'],[TYPE,MOBILE]],[/(nexus\s9)/i],[MODEL,[VENDOR,'HTC'],[TYPE,TABLET]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[VENDOR,[MODEL,/_/g,' '],[TYPE,MOBILE]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[MODEL,[VENDOR,'Acer'],[TYPE,TABLET]],[/droid.+;\s(m[1-5]\snote)\sbuild/i,/\bmz-([\w-]{2,})/i],[MODEL,[VENDOR,'Meizu'],[TYPE,MOBILE]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i,/(microsoft);\s(lumia[\s\w]+)/i,/(lenovo)[_\s-]?([\w-]+)/i,/linux;.+(jolla);/i,/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[VENDOR,MODEL,[TYPE,MOBILE]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i,/[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,/[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,/\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,/\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i],[VENDOR,MODEL,[TYPE,TABLET]],[/\s(surface\sduo)\s/i],[MODEL,[VENDOR,'Microsoft'],[TYPE,TABLET]],[/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],[MODEL,[VENDOR,'Fairphone'],[TYPE,MOBILE]],[/\s(u304aa)\sbuild/i],[MODEL,[VENDOR,'AT&T'],[TYPE,MOBILE]],[/sie-(\w*)/i],[MODEL,[VENDOR,'Siemens'],[TYPE,MOBILE]],[/[;\/]\s?(rct\w+)\sbuild/i],[MODEL,[VENDOR,'RCA'],[TYPE,TABLET]],[/[;\/\s](venue[\d\s]{2,7})\sbuild/i],[MODEL,[VENDOR,'Dell'],[TYPE,TABLET]],[/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],[MODEL,[VENDOR,'Verizon'],[TYPE,TABLET]],[/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],[MODEL,[VENDOR,'Barnes & Noble'],[TYPE,TABLET]],[/[;\/]\s(tm\d{3}\w+)\sbuild/i],[MODEL,[VENDOR,'NuVision'],[TYPE,TABLET]],[/;\s(k88)\sbuild/i],[MODEL,[VENDOR,'ZTE'],[TYPE,TABLET]],[/;\s(nx\d{3}j)\sbuild/i],[MODEL,[VENDOR,'ZTE'],[TYPE,MOBILE]],[/[;\/]\s?(gen\d{3})\sbuild.*49h/i],[MODEL,[VENDOR,'Swiss'],[TYPE,MOBILE]],[/[;\/]\s?(zur\d{3})\sbuild/i],[MODEL,[VENDOR,'Swiss'],[TYPE,TABLET]],[/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],[MODEL,[VENDOR,'Zeki'],[TYPE,TABLET]],[/[;\/]\s([yr]\d{2})\sbuild/i,/[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],[[VENDOR,'Dragon Touch'],MODEL,[TYPE,TABLET]],[/[;\/]\s?(ns-?\w{0,9})\sbuild/i],[MODEL,[VENDOR,'Insignia'],[TYPE,TABLET]],[/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],[MODEL,[VENDOR,'NextBook'],[TYPE,TABLET]],[/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],[[VENDOR,'Voice'],MODEL,[TYPE,MOBILE]],[/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],[[VENDOR,'LvTel'],MODEL,[TYPE,MOBILE]],[/;\s(ph-1)\s/i],[MODEL,[VENDOR,'Essential'],[TYPE,MOBILE]],[/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],[MODEL,[VENDOR,'Envizen'],[TYPE,TABLET]],[/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],[MODEL,[VENDOR,'MachSpeed'],[TYPE,TABLET]],[/[;\/]\s?tu_(1491)\sbuild/i],[MODEL,[VENDOR,'Rotor'],[TYPE,TABLET]],[/(shield[\w\s]+)\sbuild/i],[MODEL,[VENDOR,'Nvidia'],[TYPE,TABLET]],[/(sprint)\s(\w+)/i],[VENDOR,MODEL,[TYPE,MOBILE]],[/(kin\.[onetw]{3})/i],[[MODEL,/\./g,' '],[VENDOR,'Microsoft'],[TYPE,MOBILE]],[/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[MODEL,[VENDOR,'Zebra'],[TYPE,TABLET]],[/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],[MODEL,[VENDOR,'Zebra'],[TYPE,MOBILE]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[VENDOR,MODEL,[TYPE,CONSOLE]],[/droid.+;\s(shield)\sbuild/i],[MODEL,[VENDOR,'Nvidia'],[TYPE,CONSOLE]],[/(playstation\s[345portablevi]+)/i],[MODEL,[VENDOR,'Sony'],[TYPE,CONSOLE]],[/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],[MODEL,[VENDOR,'Microsoft'],[TYPE,CONSOLE]],[/smart-tv.+(samsung)/i],[VENDOR,[TYPE,SMARTTV]],[/hbbtv.+maple;(\d+)/i],[[MODEL,/^/,'SmartTV'],[VENDOR,'Samsung'],[TYPE,SMARTTV]],[/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i,],[[VENDOR,'LG'],[TYPE,SMARTTV]],[/(apple)\s?tv/i],[VENDOR,[MODEL,'Apple TV'],[TYPE,SMARTTV]],[/crkey/i],[[MODEL,'Chromecast'],[VENDOR,'Google'],[TYPE,SMARTTV]],[/droid.+aft([\w])(\sbuild\/|\))/i],[MODEL,[VENDOR,'Amazon'],[TYPE,SMARTTV]],[/\(dtv[\);].+(aquos)/i],[MODEL,[VENDOR,'Sharp'],[TYPE,SMARTTV]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[VENDOR,util.trim],[MODEL,util.trim],[TYPE,SMARTTV]],[/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],[[TYPE,SMARTTV]],[/((pebble))app\/[\d\.]+\s/i],[VENDOR,MODEL,[TYPE,WEARABLE]],[/droid.+;\s(glass)\s\d/i],[MODEL,[VENDOR,'Google'],[TYPE,WEARABLE]],[/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],[MODEL,[VENDOR,'Zebra'],[TYPE,WEARABLE]],[/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],[VENDOR,[TYPE,EMBEDDED]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[MODEL,[TYPE,MOBILE]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[MODEL,[TYPE,TABLET]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[TYPE,util.lowerize]],[/(android[\w\.\s\-]{0,9});.+build/i],[MODEL,[VENDOR,'Generic']],[/(phone)/i],[[TYPE,MOBILE]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[VERSION,[NAME,'EdgeHTML']],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[VERSION,[NAME,'Blink']],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[NAME,VERSION],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[VERSION,NAME]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[NAME,VERSION],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],[NAME,[VERSION,mapper.str,maps.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[NAME,'Windows'],[VERSION,mapper.str,maps.os.windows.version]],[/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i,/cfnetwork\/.+darwin/i],[[VERSION,/_/g,'.'],[NAME,'iOS']],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],[[NAME,'Mac OS'],[VERSION,/_/g,'.']],[/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/\((series40);/i],[NAME,VERSION],[/\(bb(10);/i],[VERSION,[NAME,'BlackBerry']],[/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],[VERSION,[NAME,'Symbian']],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[NAME,'Firefox OS']],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[VERSION,[NAME,'webOS']],[/crkey\/([\d\.]+)/i],[VERSION,[NAME,'Chromecast']],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[NAME,'Chromium OS'],VERSION],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(xbox);\s+xbox\s([^\);]+)/i,/(mint)[\/\s\(\)]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i,/\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku)\s(\w+)/i],[NAME,VERSION],[/(sunos)\s?([\w\.\d]*)/i],[[NAME,'Solaris'],VERSION],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[NAME,VERSION]]};var UAParser=function(ua,extensions){if(typeof ua==='object'){extensions=ua;ua=undefined;}
if(!(this instanceof UAParser)){return new UAParser(ua,extensions).getResult();}
var _ua=ua||((typeof window!=='undefined'&&window.navigator&&window.navigator.userAgent)?window.navigator.userAgent:EMPTY);var _rgxmap=extensions?util.extend(regexes,extensions):regexes;this.getBrowser=function(){var _browser={name:undefined,version:undefined};mapper.rgx.call(_browser,_ua,_rgxmap.browser);_browser.major=util.major(_browser.version);return _browser;};this.getCPU=function(){var _cpu={architecture:undefined};mapper.rgx.call(_cpu,_ua,_rgxmap.cpu);return _cpu;};this.getDevice=function(){var _device={vendor:undefined,model:undefined,type:undefined};mapper.rgx.call(_device,_ua,_rgxmap.device);return _device;};this.getEngine=function(){var _engine={name:undefined,version:undefined};mapper.rgx.call(_engine,_ua,_rgxmap.engine);return _engine;};this.getOS=function(){var _os={name:undefined,version:undefined};mapper.rgx.call(_os,_ua,_rgxmap.os);return _os;};this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()};};this.getUA=function(){return _ua;};this.setUA=function(ua){_ua=(typeof ua===STR_TYPE&&ua.length>UA_MAX_LENGTH)?util.trim(ua,UA_MAX_LENGTH):ua;return this;};this.setUA(_ua);return this;};UAParser.VERSION=LIBVERSION;UAParser.BROWSER={NAME:NAME,MAJOR:MAJOR,VERSION:VERSION};UAParser.CPU={ARCHITECTURE:ARCHITECTURE};UAParser.DEVICE={MODEL:MODEL,VENDOR:VENDOR,TYPE:TYPE,CONSOLE:CONSOLE,MOBILE:MOBILE,SMARTTV:SMARTTV,TABLET:TABLET,WEARABLE:WEARABLE,EMBEDDED:EMBEDDED};UAParser.ENGINE={NAME:NAME,VERSION:VERSION};UAParser.OS={NAME:NAME,VERSION:VERSION};if(typeof(exports)!==UNDEF_TYPE){if(typeof module!==UNDEF_TYPE&&module.exports){exports=module.exports=UAParser;}
exports.UAParser=UAParser;}else{if(typeof(define)==='function'&&define.amd){define(function(){return UAParser;});}else if(typeof window!=='undefined'){window.UAParser=UAParser;}}
var $=typeof window!=='undefined'&&(window.jQuery||window.Zepto);if($&&!$.ua){var parser=new UAParser();$.ua=parser.getResult();$.ua.get=function(){return parser.getUA();};$.ua.set=function(uastring){parser.setUA(uastring);var result=parser.getResult();for(var prop in result){$.ua[prop]=result[prop];}};}})(typeof window==='object'?window:this);},{}]},{},[11])(11)});